{"product_card": {"add_to_cart": "Add to Cart", "please_login": "Please log in", "unexpected_error": "An unexpected error occurred. Please try again later.", "out_of_stock": "Out of stock", "price_label": "Price", "author_label": "Author", "details_button": "Details", "added_to_wishlist_success": "Product added to wishlist successfully", "error_adding_to_wishlist": "An error occurred while adding the product to the wishlist", "unexpected_error_adding_to_wishlist": "An unexpected error occurred while adding the product to the wishlist"}, "product_details": {"product_details": "Product Details", "related_products": "Related Products", "weight": "Weight", "packaging": "Packaging", "material": "Material", "pages": "Pages", "dimensions": "Dimensions", "buy_now": "Buy Now", "add_to_cart": "Add to Cart", "quantity": "Quantity", "subject": "Subject", "release_year": "Release Year", "edition": "Edition", "publisher": "Publisher", "show_all": "Show All", "reviews": "Reviews", "all_ratings": "All Ratings", "add_review": "Add Review", "add_rating": "Add Rating", "add_review_placeholder": "Great product I recommend it", "add_review_button": "Post Review"}, "footer": {"links": {"headline": "Important Links", "about": "About", "contact": "Contact", "releases_list": "Releases list", "distributors": "Distributors", "terms": "Terms", "friends": {"headline": "Related Websites", "1": "Nasseq", "2": "Nasseq Academy"}}, "description": "This text is a placeholder for text that can be replaced in the same space, this text was generated from the Arabic text generator", "copyright": "All rights reserved © for Dar Al Fajr 2024"}, "common": {"save": "Save", "edit": "Edit", "submit": "Submit", "delete": "Delete", "delete_confirmation": "Are you sure you want to delete this item?", "delete_success": "Item deleted successfully", "delete_error": "An error occurred while deleting the item", "cancel": "Cancel", "confirm": "Confirm", "close": "Close", "view_details": "View Details", "add_to_cart": "Add to Cart", "profile": "Profile", "home": "Home", "search": "Search", "send": "Send", "activate": "Activate", "apply": "Apply"}, "nav": {"news": "Latest Releases", "issues_list": "Releases", "nasq": "Nasseq"}, "auth": {"login": "<PERSON><PERSON>", "register": "Register", "forgot_password": "Forgot Password", "dont_have_account": "Don't have an account?", "have_account": "Have an account?", "reset_password": "Reset Password", "verify_email": "<PERSON><PERSON><PERSON>", "verify_email_sent": "Email verification sent", "logout": "Logout", "logout_confirmation": "Are you sure you want to logout?", "logout_success": "Logout successful", "logout_error": "An error occurred while logging out", "unexpected_login_error": "An unexpected error occurred while logging in", "login_error_message": "The email or password is incorrect", "login_success": "Login successful", "register_success": "Register successful", "register_error": "An error occurred while registering", "welcome_back": "Welcome back 👋", "create_account": "Create account", "create_account_description": "Sign in to follow all the latest books", "remember_me": "Remember me", "forgot_password_question": "Forgot password?", "forgot_password_link": "Forgot password?", "login_with_google": "Login with Google", "login_with_facebook": "Login with Facebook", "login_with_apple": "Login with Apple", "login_with_twitter": "Login with Twitter", "login_with_linkedin": "Login with LinkedIn"}, "profile": {"user_data": {"user_data": "User Data", "firstName": "First Name", "lastName": "Last Name", "email": "Email", "phone": "Phone", "city": "City", "district": "District", "firstNamePlaceholder": "Enter your first name here", "lastNamePlaceholder": "Enter your last name here", "emailPlaceholder": "Enter your email here", "phonePlaceholder": "Enter your phone here", "cityPlaceholder": "City", "districtPlaceholder": "District"}, "password": {"password": "Password", "current_password": "Current Password", "new_password": "New Password", "confirm_password": "Confirm Password", "new_password_min_length": "Password must be at least 8 characters", "new_password_required": "New password is required", "password_not_match": "Password does not match", "confirm_password_required": "Confirm password is required"}, "orders": {"order_number": "Order Number", "date": "Date", "payment_method": "Payment Method", "order_status": "Order Status", "total": "Total", "actions": "Actions", "cancel_order": "Cancel Order", "cancel_order_confirmation": "Are you sure you want to cancel the order?", "order_details": "Order Details", "edit_order": "Edit Order", "order_confirmed": "Order Confirmed", "order_cancelled": "Order Cancelled", "order_completed": "Order Completed", "order_pending": "Order Pending", "order_shipped": "Order Shipped", "order_delivered": "Order Delivered", "empty": "There are no orders at the moment."}, "favourite": {"product": "Product", "price": "Price", "quantity": "Quantity", "total": "Total", "actions": "Actions", "remove_from_favourite": "<PERSON><PERSON><PERSON> from favourite", "item_removed": "Product removed from favourites successfully", "error_removing_item": "An error occurred while removing the product from favourites", "unexpected_error_removing_item": "An unexpected error occurred while removing the product from favourites", "error_adding_item": "An error occurred while adding the product to favourites", "unexpected_error_adding_item": "An unexpected error occurred while adding the product to favourites", "empty": "There are no books added to my favourite."}, "profile_data": "Profile Data", "my_orders": "My Orders", "my_favourite": "My Favourite", "change_password": "Change Password"}, "category": "Category", "filter": {"sort_by_price": "Sort by Price", "sort_by_name": "Sort by Name", "sort_by_date": "Sort by Date", "sort_by_rating": "Sort by Rating", "sort_by_popularity": "Sort by Popularity", "sort_by_price_none": "...", "asc": "Ascending", "desc": "Descending"}, "contact_us": {"contact_us": "Contact Us", "contact_us_details": "Contact Details", "contact_us_via": "Reach us on:", "our_location_on_map": "Our Location on the Map", "contact_us_description": "You can contact us via email or phone", "message": "Message", "message_placeholder": "Message text", "address": "Address", "phone": "Phone", "email": "Email", "customer_support": "Customer Support", "instagram": "Instagram", "x": "X", "facebook": "Facebook", "telegram": "Telegram", "social_media": "Social Media"}, "payment": {"payment_success": "Payment Successful", "view_order": "View Order", "payment_data": "Payment Data", "payment_method": "Payment Method", "payment_method_placeholder": "Enter payment method here", "payment_method_description": "Please select the appropriate payment method for you", "payment_method_cash": "Payment on Delivery", "payment_method_bank": "Payment via Bank"}, "cart": {"product": "Product", "price": "Price", "quantity": "Quantity", "total": "Total", "item_removed": "Product removed from cart successfully", "error_removing_item": "An error occurred while removing the product from the cart", "item_added": "Product added to cart successfully", "error_adding_item": "An error occurred while adding the product to the cart", "empty": "Cart is empty"}, "coupon": {"coupon": "coupon", "have_discount_question": "Do you have a discount coupon?", "coupon_code": "Coupon Code", "coupon_applied": "Coupon applied successfully", "discount": "Discount :", "discount_label": "Discount value", "delivery": "Delivery", "tax": "Tax", "total": "Total :", "error_applying_coupon": "An error occurred while applying the coupon", "unexpected_error_applying_coupon": "An unexpected error occurred while applying the coupon"}, "search": "Search for the book you want ...", "search_not_found": "No results found", "coming_soon": {"coming_soon": "Coming Soon", "description": "We're working hard to bring you something amazing. Stay tuned for updates!", "back_to_home": "Back to Home", "contact_us": "Contact Us"}}