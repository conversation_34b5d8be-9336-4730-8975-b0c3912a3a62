{"product_card": {"add_to_cart": "Sepete Ekle", "please_login": "Lütfen giriş ya<PERSON>ın", "unexpected_error": "Beklenmeyen bir hata o<PERSON>. Lütfen daha sonra tekrar deneyin.", "out_of_stock": "Stokta yok", "price_label": "<PERSON><PERSON><PERSON>", "author_label": "<PERSON><PERSON>", "details_button": "Detaylar", "added_to_wishlist_success": "Ürün başarıyla listeye eklendi", "error_adding_to_wishlist": "<PERSON><PERSON>ün listeye eklenirken bir hata oluş<PERSON>", "unexpected_error_adding_to_wishlist": "Ürün listeye eklenirken beklenmeyen bir hata oluş<PERSON>"}, "product_details": {"product_details": "<PERSON><PERSON><PERSON><PERSON>", "related_products": "<PERSON>lgi<PERSON>", "weight": "Ağırlık", "packaging": "Paketleme", "material": "Malzeme", "pages": "Sayfa Sayısı", "dimensions": "Boyutlar", "buy_now": "Şimdi Satın Al", "add_to_cart": "Sepete Ekle", "quantity": "<PERSON><PERSON>", "subject": "<PERSON><PERSON>", "release_year": "Yayınlanma Yılı", "edition": "Baskı No", "publisher": "Yayınlayan", "show_all": "Tümünü <PERSON>", "reviews": "<PERSON><PERSON><PERSON>", "all_ratings": "<PERSON><PERSON><PERSON>", "add_review": "<PERSON><PERSON>", "add_rating": "Değerlendirme Ekle", "add_review_placeholder": "Harika bir ü<PERSON>ü<PERSON>, öneririm", "add_review_button": "<PERSON><PERSON>"}, "footer": {"links": {"headline": "<PERSON><PERSON><PERSON><PERSON>", "about": "Hakkımızda", "contact": "İletişim", "releases_list": "Ya<PERSON><PERSON>nlar", "distributors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "terms": "Kullanım Şartları", "friends": {"headline": "İlgili Websites", "1": "Nasseq", "2": "Nasseq Academy"}}, "description": "<PERSON><PERSON> metin, a<PERSON><PERSON> alanda <PERSON>tirilebilen metin için bir yer tutucudur, bu metin, <PERSON><PERSON> metin oluşturucusundan oluşturulmuştur", "copyright": "Tüm Hakları Saklıdır © Dar Al Fajr 2024"}, "nav": {"news": "<PERSON>", "issues_list": "Ya<PERSON><PERSON>nlar", "nasq": "Nasseq"}, "auth": {"login": "<PERSON><PERSON><PERSON>", "register": "<PERSON><PERSON><PERSON>", "forgot_password": "Şif<PERSON><PERSON>", "reset_password": "Şifremi Sıfırla", "dont_have_account": "<PERSON><PERSON><PERSON><PERSON>n yok mu?", "create_account": "<PERSON><PERSON><PERSON>", "create_account_description": "Tüm yeni kitapları takip etmek için giriş yapın", "have_account": "<PERSON><PERSON><PERSON><PERSON>n var mı?", "verify_email": "<PERSON><PERSON>", "verify_email_sent": "<PERSON><PERSON>", "logout": "Çıkış Yap", "logout_confirmation": "<PERSON><PERSON><PERSON><PERSON><PERSON> yapmak istediğinizden emin misiniz?", "logout_success": "Çıkış yapma işlemi başarılı", "logout_error": "Çıkış yapma işlemi sırasında bir hata oluştu", "unexpected_login_error": "<PERSON><PERSON><PERSON> yapma işlemi sırasında beklenmeyen bir hata oluştu", "login_error_message": "Email veya <PERSON>ı", "login_success": "<PERSON><PERSON><PERSON> yapm<PERSON> işlemi başarılı", "register_success": "Kayıt işlemi başarılı", "register_error": "Kayıt işlemi sırasında bir hata oluştu", "welcome_back": "<PERSON><PERSON><PERSON> 👋", "remember_me": "<PERSON><PERSON>", "forgot_password_question": "Şifrenizi mi unuttunuz?", "forgot_password_link": "Şifremi Sıfırla", "login_with_google": "Google ile Giriş <PERSON>", "login_with_facebook": "Facebook ile Giriş <PERSON>", "login_with_apple": "Apple ile Giriş <PERSON>", "login_with_twitter": "Twitter ile G<PERSON>", "login_with_linkedin": "LinkedIn ile Giri<PERSON>"}, "category": "<PERSON><PERSON><PERSON>", "filter": {"sort_by_price": "Fiyata Gö<PERSON>", "sort_by_name": "<PERSON><PERSON><PERSON>", "sort_by_date": "<PERSON><PERSON><PERSON>", "sort_by_rating": "Değerlendirmeye Göre Sırala", "sort_by_popularity": "Popülerliğe Göre Sırala", "sort_by_price_none": "...", "asc": "En Düşük", "desc": "<PERSON>"}, "common": {"save": "<PERSON><PERSON>", "edit": "<PERSON><PERSON><PERSON><PERSON>", "submit": "<PERSON><PERSON><PERSON>", "delete": "Sil", "delete_confirmation": "<PERSON>u öğeyi silmek istediğinizden emin misiniz?", "delete_success": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON><PERSON> si<PERSON>", "delete_error": "<PERSON><PERSON><PERSON> si<PERSON>en bir hata oluş<PERSON>", "error": "<PERSON><PERSON>", "cancel": "İptal", "close": "Ka<PERSON><PERSON>", "confirm": "<PERSON><PERSON><PERSON>", "view_details": "Detayları Gör", "add_to_cart": "Sepete Ekle", "profile": "Profil", "home": "<PERSON><PERSON><PERSON>", "search": "<PERSON><PERSON>", "send": "<PERSON><PERSON><PERSON>", "activate": "Aktive Et", "apply": "<PERSON><PERSON><PERSON><PERSON>"}, "profile": {"user_data": {"firstName": "Ad<PERSON>", "lastName": "Soyadı", "email": "Email", "phone": "Telefon", "city": "Şehir", "district": "İlçe", "firstNamePlaceholder": "Adınızı giriniz", "lastNamePlaceholder": "Soyadınızı giriniz", "emailPlaceholder": "Emailinizi giri<PERSON>z", "phonePlaceholder": "Telefonunuzu giriniz", "cityPlaceholder": "Şehir", "districtPlaceholder": "İlçe"}, "password": {"password": "Şifre", "current_password": "Mevcut <PERSON>", "new_password": "<PERSON><PERSON>", "confirm_password": "<PERSON><PERSON>", "new_password_min_length": "Şifre en az 8 karakter olmalıdır", "new_password_required": "<PERSON><PERSON>", "password_not_match": "<PERSON><PERSON><PERSON> e<PERSON>şmiyor", "confirm_password_required": "<PERSON><PERSON> doğrulama gereklidir"}, "orders": {"order_number": "Sipariş No", "date": "<PERSON><PERSON><PERSON>", "payment_method": "<PERSON><PERSON><PERSON>", "order_status": "Sipariş Durumu", "total": "Toplam", "actions": "İşlemler", "cancel_order": "Siparişi İptal Et", "cancel_order_confirmation": "Siparişi iptal etmek istediğinizden emin misiniz?", "order_details": "Sipariş Detayları", "edit_order": "Sipariş<PERSON>", "order_confirmed": "Sipariş Onaylandı", "order_cancelled": "Sipariş İptal Edildi", "order_completed": "Sipariş Tamamlandı", "order_pending": "Sipariş Bekliyor", "order_shipped": "Sipariş Gönderildi", "order_delivered": "Sipariş Teslim Edildi", "empty": "<PERSON><PERSON> anda herhangi bir sipariş bulunmamaktadır."}, "favourite": {"product": "<PERSON><PERSON><PERSON><PERSON>", "price": "<PERSON><PERSON><PERSON>", "quantity": "<PERSON><PERSON>", "total": "Toplam", "actions": "İşlemler", "remove_from_favourite": "Favorilerden Kaldır", "item_removed": "Ürün <PERSON>ilerden başarıyla kaldırıldı", "error_removing_item": "<PERSON>rün favorilerden kaldırılırken bir hata oluştu", "unexpected_error_removing_item": "<PERSON>rün favorilerden kaldırılırken beklenmeyen bir hata oluştu", "error_adding_item": "<PERSON><PERSON>ü<PERSON> favorilerden eklenirken bir hata oluş<PERSON>", "unexpected_error_adding_item": "<PERSON><PERSON>ün favorilerden eklenirken beklenmeyen bir hata olu<PERSON>", "empty": "Favorilerime eklenen kitap yok."}, "profile_data": "<PERSON><PERSON><PERSON>", "my_orders": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "my_favourite": "<PERSON>av<PERSON><PERSON><PERSON>", "change_password": "<PERSON>if<PERSON><PERSON>"}, "contact_us": {"contact_us": "Bize Ulaşın", "contact_us_details": "İletişim Bilgileri", "contact_us_via": "Bize Ulaşın:", "our_location_on_map": "<PERSON><PERSON><PERSON>", "contact_us_description": "Email veya telefon ile bize ulaşabilirsiniz", "message": "<PERSON><PERSON>", "message_placeholder": "Mesajınızı giriniz", "address": "<PERSON><PERSON>", "phone": "Telefon", "email": "Email"}, "payment": {"payment_success": "Ödeme Başarılı", "view_order": "Siparişi Gö<PERSON>", "payment_data": "Ödeme Bilgileri", "payment_method": "<PERSON><PERSON><PERSON>", "payment_method_placeholder": "Ödeme yönteminizi giriniz", "payment_method_description": "Lütfen uygun ödeme yöntemini seçiniz", "payment_method_cash": "<PERSON><PERSON><PERSON><PERSON> Ödeme", "payment_method_bank": "Banka Üzerinden Ödeme"}, "cart": {"product": "<PERSON><PERSON><PERSON><PERSON>", "price": "<PERSON><PERSON><PERSON>", "quantity": "<PERSON><PERSON>", "total": "Toplam", "item_removed": "Ürün sepetten başarıyla kaldırıldı", "error_removing_item": "<PERSON>rün sepetten kaldırılırken bir hata oluştu", "item_added": "<PERSON><PERSON><PERSON><PERSON> sepete başarıyla eklendi", "error_adding_item": "<PERSON><PERSON><PERSON><PERSON> sepete eklenirken bir hata oluş<PERSON>", "empty": "<PERSON><PERSON>"}, "coupon": {"coupon": "<PERSON><PERSON><PERSON>", "have_discount_question": "<PERSON><PERSON><PERSON> k<PERSON>uz var mı?", "coupon_code": "<PERSON><PERSON><PERSON>", "coupon_applied": "Kupon başarıyla uygulandı", "discount": "İndirim :", "discount_label": "<PERSON><PERSON><PERSON>", "delivery": "Teslimat", "tax": "<PERSON><PERSON><PERSON>", "total": "Toplam :", "error_applying_coupon": "Ku<PERSON>n uygulanırken bir hata oluştu", "unexpected_error_applying_coupon": "Kupon uygulanırken beklenmeyen bir hata oluştu"}}