'use client';

import merge from 'lodash/merge';
import { enUS as enUSAdapter, arSA as arSAAdapter, tr as trAdapter } from 'date-fns/locale';
// core
import { enUS as enUSCore, arSA as arSACore, trTR as trTRCore } from '@mui/material/locale';
// date-pickers
import { enUS as enUSDate, trTR as trTRDate } from '@mui/x-date-pickers/locales';
// data-grid
import { enUS as enUSDataGrid, arSD as arSDDataGrid, trTR as trTRDataGrid } from '@mui/x-data-grid';

// PLEASE REMOVE `LOCAL STORAGE` WHEN YOU CHANGE SETTINGS.
// ----------------------------------------------------------------------

export const allLangs = [
  {
    label: 'Arabic',
    value: 'ar',
    systemValue: merge(arSDDataGrid, arSACore),
    adapterLocale: arSAAdapter,
    icon: 'flagpack:sa',
  },
  {
    label: 'English',
    value: 'en',
    systemValue: merge(enUSDate, enUSDataGrid, enUSCore),
    adapterLocale: enUSAdapter,
    icon: 'flagpack:gb-nir',
  },
  {
    label: 'Turkish',
    value: 'tr',
    systemValue: merge(trTRDate, trTRDataGrid, trTRCore),
    adapterLocale: trAdapter,
    icon: 'flagpack:tr',
  },
];

export const defaultLang = allLangs[0]; // Arabic (default)

// GET MORE COUNTRY FLAGS
// https://icon-sets.iconify.design/flagpack/
// https://www.dropbox.com/sh/nec1vwswr9lqbh9/AAB9ufC8iccxvtWi3rzZvndLa?dl=0
