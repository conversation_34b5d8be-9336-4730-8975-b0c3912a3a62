import axios, { AxiosRequestConfig } from 'axios';
// config
import { BASE_API_URL } from 'src/config-global';

// ----------------------------------------------------------------------
const LANG = 'i18nextLng';
const STORAGE_KEY = 'accessToken';


const axiosInstance = axios.create({ baseURL: BASE_API_URL });

axiosInstance.interceptors.request.use(
  (config) => {

    const lang = localStorage.getItem(LANG) || 'ar';
    const accessToken = sessionStorage.getItem(STORAGE_KEY);

    config.headers['Accept-Language'] = lang;
    config.headers.Authorization = accessToken ? `Bearer ${accessToken}` : '';
    return config;
  },
  (error) => Promise.reject(error)
);

axiosInstance.interceptors.response.use(
  (res) => res,
  (error) => Promise.reject((error.response && error.response.data) || 'Something went wrong')
);

export default axiosInstance;

// ----------------------------------------------------------------------

export const fetcher = async (args: string | [string, AxiosRequestConfig]) => {
  const [url, config] = Array.isArray(args) ? args : [args];

  const res = await axiosInstance.get(url, { ...config });

  return res.data;
};

// ----------------------------------------------------------------------

export const endpoints = {
  chat: 'chat',
  kanban: 'kanban',
  calendar: 'calendar',
  home: 'home',
  profile: {
    me: 'profile',
    carts: 'carts',
    removeFromCart: (id: number) => `carts/${id}`,
    orders: '/orders',
    favorites: 'favourites',
    changePassword: '/change-password',
  },
  auth: {
    login: 'login',
    register: 'register',
  },
  mail: {
    list: 'mail/list',
    details: 'mail/details',
    labels: 'mail/labels',
  },
  post: {
    list: 'post/list',
    details: 'post/details',
    latest: 'post/latest',
    search: 'post/search',
  },
  product: {
    list: 'product/list',
    reviews: (id: string) => `product/reviews/${id}`,
    details: (id: string) => `products/${id}`,
    search: 'product/search',
    byCategory: (id: string) => `categories/${id}`,
  },
  categories: 'categories',
};
