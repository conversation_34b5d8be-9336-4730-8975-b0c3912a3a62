import useSWR from 'swr';
import { useMemo } from 'react';

// utils
import axiosInstance, { fetcher, endpoints } from 'src/utils/axios';
// types
import { IProductItem, IPaginationInfo, IReview } from 'src/types/product';

// ----------------------------------------------------------------------

export function useGetProducts() {
  const URL = endpoints.product.list;

  const { data, isLoading, error, isValidating } = useSWR(URL, fetcher);

  const memoizedValue = useMemo(
    () => ({
      products: (data?.products as IProductItem[]) || [],
      productsLoading: isLoading,
      productsError: error,
      productsValidating: isValidating,
      productsEmpty: !isLoading && !data?.products.length,
    }),
    [data?.products, error, isLoading, isValidating]
  );

  return memoizedValue;
}

// ----------------------------------------------------------------------

export function useGetProduct(productId: string) {
  const URL = productId ? [endpoints.product.details(productId)] : null;

  const { data, isLoading, error, isValidating } = useSWR(URL, fetcher);

  const memoizedValue = useMemo(
    () => ({
      bookDetails: data?.data as IProductItem,
      bookDetailsLoading: isLoading,
      bookDetailsError: error,
      bookDetailsValidating: isValidating,
    }),
    [data, error, isLoading, isValidating]
  );

  return memoizedValue;
}

export function useGetProductByCategory(categoryId: string) {
  const URL = categoryId ? [endpoints.product.byCategory(categoryId)] : null;

  const { data, isLoading, error, isValidating } = useSWR(URL, fetcher);
  const memoizedValue = useMemo(
    () => ({
      products_by_category: data?.products_by_category[0],
      isLoadingProduct: isLoading,
      productError: error,
      productValidating: isValidating,
    }),
    [data, error, isLoading, isValidating]
  );

  return memoizedValue;
}
// ----------------------------------------------------------------------

export function useSearchProducts(query: string) {
  const URL = query ? [endpoints.product.search, { params: { query } }] : null;

  const { data, isLoading, error, isValidating } = useSWR(URL, fetcher, {
    keepPreviousData: true,
  });

  const memoizedValue = useMemo(
    () => ({
      searchResults: (data?.results as IProductItem[]) || [],
      searchLoading: isLoading,
      searchError: error,
      searchValidating: isValidating,
      searchEmpty: !isLoading && !data?.results.length,
    }),
    [data?.results, error, isLoading, isValidating]
  );

  return memoizedValue;
}

export function useGetProductReviews(productId: string) {
  const URL = productId ? [endpoints.product.reviews(productId)] : null;

  const { data, isLoading, error, isValidating } = useSWR(URL, fetcher);
  const memoizedValue = useMemo(
    () => ({
      reviews: data?.data as IReview[],
      paginationInfo: data?.pagination_info as IPaginationInfo,
      reviewsLoading: isLoading,
      reviewsError: error,
      reviewsValidating: isValidating,
      reviewsEmpty: !isLoading && !data?.data.length,
    }),
    [data?.data, data?.pagination_info, error, isLoading, isValidating]
  );

  return memoizedValue;
}

export function useToggleFavorite() {
  const URL = endpoints.profile.favorites;

  const handleToggleFavorite = async (productId: number) => {
    try {
      const response = await axiosInstance.post(URL, {
        product_id: productId,
      });
      return { success: true, data: response.data };
    } catch (error: any) {
      // Handle specific error cases
      if (error.response?.status === 404) {
        throw new Error('Favorites endpoint not found');
      }
      if (error.response?.status === 403) {
        throw new Error('CORS error: Not allowed to access this resource');
      }
      // Generic error
      if (error.message === 'Unauthenticated.') {
        return { success: false, message: 'Unauthenticated' };
      }
      throw new Error(error || 'Failed to toggle favorite');
    }
  };

  return { handleToggleFavorite };
}
