import { useMemo } from 'react';
import useSWR from 'swr';
import axiosInstance, { fetcher, endpoints } from 'src/utils/axios';
import { ICartItem } from 'src/types/cart';

const URL = endpoints.profile.carts;

export function useCartAPIRequest() {
  const handleAddToCart = async (productId: number, qty: number) => {
    try {
      const response = await axiosInstance.post(URL, {
        product_id: productId,
        qty,
      });
      return { success: true, data: response.data };
    } catch (error: any) {
      return { success: false, error: error.message };
    }
  };

  const handleRemoveFromCart = async (id: number) => {
    const deleteURL = endpoints.profile.removeFromCart;
    try {
      const response = await axiosInstance.delete(deleteURL(id));
      return { success: true, data: response.data };
    } catch (error: any) {
      return { success: false, error: error.message };
    }
  };

  return {
    handleAddToCart,
    handleRemoveFromCart,
  };
}
export const useGetPersonalCart = () => {
  const { data, isLoading, error, isValidating } = useSWR(URL, fetcher);
  console.log('data', data);
  const memoizedValue = useMemo(
    () => ({
      cart: data?.data?.items?.data as ICartItem[],
      cartLoading: isLoading,
      cartError: error,
      cartValidating: isValidating,
      cartEmpty: !isLoading && data?.data?.items === null,
    }),
    [data, error, isLoading, isValidating]
  );

  return memoizedValue;
};
