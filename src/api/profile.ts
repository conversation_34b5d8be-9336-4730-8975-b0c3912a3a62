import { useMemo } from 'react';
import useSWR from 'swr';
import axiosInstance, { fetcher, endpoints } from 'src/utils/axios';
import { IUser } from 'src/auth/types';
import { IPaginationInfo, IProductItem } from 'src/types/product';
import { IPasswordData } from 'src/sections/profile/types';
import { IOrder } from 'src/types/order';

export function useGetPersonalInfo() {
  const URL = endpoints.profile.me;

  const { data, isLoading, error, isValidating } = useSWR(URL, fetcher);

  const memoizedValue = useMemo(
    () => ({
      personalInfo: data as IUser,
      personalInfoLoading: isLoading,
      personalInfoError: error,
      personalInfoValidating: isValidating,
      personalInfoEmpty: !isLoading,
    }),
    [data, error, isLoading, isValidating]
  );

  return memoizedValue;
}

export function useGetPersonalOrders() {
  const URL = endpoints.profile.orders;

  const { data, isLoading, error, isValidating } = useSWR(URL, fetcher);

  const memoizedValue = useMemo(
    () => ({
      orders: data?.data as IOrder[],
      paginationInfo: data?.pagination_info as IPaginationInfo,
      ordersLoading: isLoading,
      ordersError: error,
      ordersValidating: isValidating,
      ordersEmpty: !isLoading && !data?.data.length,
    }),
    [data, error, isLoading, isValidating]
  );

  return memoizedValue;
}

export function useGetPersonalFavorites() {
  const URL = endpoints.profile.favorites;

  const { data, isLoading, error, isValidating } = useSWR(URL, fetcher);

  const memoizedValue = useMemo(
    () => ({
      favorites: data?.data as IProductItem[],
      favoritesLoading: isLoading,
      favoritesError: error,
      favoritesValidating: isValidating,
      favoritesEmpty: !isLoading && !data?.data.length,
    }),
    [data, error, isLoading, isValidating]
  );

  return memoizedValue;
}

export function useUpdateUserPassword() {
  const URL = endpoints.profile.changePassword;

  const handlePost = async (data: IPasswordData) => {
    try {
      const response = await axiosInstance.post(URL, data);
      return { success: true, data: response.data, status: response.status };
    } catch (error: any) {
      return { success: false, error, status: error.response?.status };
    }
  };

  return { handlePost };
}
