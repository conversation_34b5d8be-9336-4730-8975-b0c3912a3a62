import useSWR from 'swr';
import { useMemo } from 'react';
import { endpoints, fetcher } from 'src/utils/axios';
import { IHomeData } from 'src/types/home';

export function useGetHome() {
  const URL = endpoints.home;

  const { data, isLoading, error, isValidating } = useSWR(URL, fetcher);

  const memoizedValue = useMemo(
    () => ({
      homeData: data as IHomeData,
      isHomeLoading: isLoading,
      homeError: error,
      homeValidating: isValidating,
      homeEmpty: !isLoading && !data?.products_by_category.length,
    }),
    [data, error, isLoading, isValidating]
  );

  return memoizedValue;
}
