import { Tab } from '@mui/material';
import { useTheme } from '@mui/material/styles';
import { StyledTabs } from './styles';

const Tabs = ({
  labels,
  onChange,
  value,
}: {
  labels: string[];
  onChange: (event: React.SyntheticEvent, value: any) => void;
  value: any;
}) => {
  const theme = useTheme();
  return (
    <StyledTabs value={value} onChange={onChange}>
      {labels.map((label) => (
        <Tab
          label={label}
          sx={{
            '&.Mui-selected': {
              color: theme.palette.secondary.main,
            },
          }}
        />
      ))}
    </StyledTabs>
  );
};

export default Tabs;
