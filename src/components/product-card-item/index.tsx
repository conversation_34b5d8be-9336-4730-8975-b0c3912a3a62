import { Typo<PERSON>, Box, Tooltip, Stack } from '@mui/material';
import { useTheme } from '@mui/material/styles';
import { useState } from 'react';
import { useSnackbar } from 'notistack';
import Link from '@mui/material/Link';
import { useRouter } from 'next/navigation';
import { Icon } from '@iconify/react';
import { paths } from 'src/routes/paths';
import { RouterLink } from 'src/routes/components';
import { fCurrency } from 'src/utils/format-number';
import { useCartStore } from 'src/store/user/useCartStore';
import { IProductItem } from 'src/types/product';
import { useToggleFavorite } from 'src/api/product';
import { useCartAPIRequest } from 'src/api/cart';
// hook
import { useAuthContext } from 'src/auth/hooks';
import { useLocales } from 'src/locales';
import Iconify from '../iconify';

import {
  StyledCard,
  ImageContainer,
  ProductImage,
  WishlistButton,
  CartOverlay,
  AddToCartButton,
  ContentContainer,
  StyledCardTitle,
} from './styles';

import { StyledIconButton } from '../carousel/carousel-arrow-index';

type Props = {
  product: IProductItem;
  isCategory?: boolean;
  category?: string;
};

const ProductCard = ({ product, category, isCategory }: Props) => {
  const { authenticated } = useAuthContext();
  const { t } = useLocales();
  //  TODO: How to know if the product is available or not ? API doesn't return a property
  const {
    id,
    name,
    price_for_selling,
    is_favourite = false,
    author,
    alt_cover = name,
    cover = 'https://i.ibb.co/WgtFZNv/7-Fj1ho-Xna3-GIJxbg9hr4-WAXt-VMyd-Hbam-Niaxk-D1-X.png',
    available = true,
  } = product;
  const price = Number(fCurrency(price_for_selling));
  const [wishlist, setWishlist] = useState(is_favourite);
  const linkTo = paths.product.details(`${id}`);
  const router = useRouter();
  const { handleToggleFavorite } = useToggleFavorite();
  const theme = useTheme();
  const { enqueueSnackbar } = useSnackbar();

  const { addBook } = useCartStore();
  const { handleAddToCart } = useCartAPIRequest();

  const handleAddToCartStore = async () => {
    try {
      if (!authenticated) {
        enqueueSnackbar(t('product_card.please_login'), { variant: 'error' });
        return;
      }

      const response = await handleAddToCart(id, 1);

      if (response.success) {
        addBook(id, 1, price);
        enqueueSnackbar(t('cart.item_added'), { variant: 'success' });
      } else {
        enqueueSnackbar(t('cart.error_adding_item'), { variant: 'error' });
      }
    } catch (error) {
      enqueueSnackbar(t('product_card.unexpected_error'), { variant: 'error' });
      console.error('Error in handleAddToCartStore:', error);
    }
  };

  const handleAddToWishlist = async () => {
    try {
      if (!authenticated) {
        enqueueSnackbar(t('product_card.please_login'), { variant: 'error' });
        return;
      }
      const response = await handleToggleFavorite(id);
      if (response.success) {
        setWishlist(!wishlist);
        enqueueSnackbar(t('product_card.added_to_wishlist_success'), { variant: 'success' });
      } else {
        enqueueSnackbar(t('product_card.error_adding_to_wishlist'), { variant: 'error' });
      }
    } catch (error) {
      enqueueSnackbar(t('product_card.unexpected_error_adding_to_wishlist'), { variant: 'error' });
      console.error('Error in handleAddToWishlist:', error);
    }
  };

  return (
    <StyledCard
      sx={{
        borderRadius: isCategory ? '0' : '30px',
        direction: theme.direction,
        mx: '4px',
      }}
    >
      <Tooltip title={!available && 'Out of stock'} placement="bottom-end">
        <ImageContainer>
          <ProductImage
            src={cover}
            alt={alt_cover}
            onClick={() => router.push(linkTo)}
            style={{ cursor: 'pointer' }}
          />

          <WishlistButton theme={theme} isFavorite={wishlist} onClick={handleAddToWishlist}>
            <Iconify
              color={theme.palette.common.white}
              icon={wishlist ? 'mdi:heart' : 'mdi:heart-outline'}
            />
          </WishlistButton>

          {!!available && (
            <>
              <CartOverlay className="cart-overlay" />
              <AddToCartButton className="add-to-cart-button" onClick={handleAddToCartStore}>
                <Iconify icon="solar:cart-plus-bold" width={24} />
                <Typography sx={{ px: 1 }}>{t('product_card.add_to_cart')}</Typography>
              </AddToCartButton>
            </>
          )}
        </ImageContainer>
      </Tooltip>

      <ContentContainer>
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            direction: theme.direction,
            gap: 2,
          }}
        >
          <Link component={RouterLink} href={`${linkTo}?category=${category}`}>
            <StyledCardTitle
              variant="body1"
              sx={{ xs: { fontSize: '18px' }, sm: { fontSize: '22px' }, md: { fontSize: '26px' } }}
            >
              {name}
            </StyledCardTitle>
          </Link>

          <Typography
            variant="body1"
            sx={() => ({
              color: theme.palette.grey[400],
              fontSize: {
                xs: '12px',
                sm: '16px',
                md: '20px',
              },
              lineHeight: 1.2,
            })}
          >
            {author}
          </Typography>
        </Box>

        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            direction: theme.direction,
            gap: 1,
            width: '100%',
          }}
        >
          <Stack direction="row" gap={1}>
            <Iconify
              icon="mdi:tag-outline"
              width={30}
              height={30}
              sx={{ color: theme.palette.primary.main }}
            />
            <Typography
              variant="body1"
              sx={() => ({
                color: theme.palette.primary.main,
                fontWeight: 'bold',
                fontSize: {
                  xs: '1.5rem',
                  sm: '1.65rem',
                  md: '1.813rem',
                },
                lineHeight: 1.2,
              })}
            >
              {fCurrency(price_for_selling)}
            </Typography>
          </Stack>
          {!isCategory && (
            <StyledIconButton
              sx={{
                width: 36,
                height: 36,
              }}
              color="inherit"
              onClick={() => {
                router.push(paths.product.details(`${id}`), {});
              }}
            >
              <Icon
                icon="oui:arrow-right"
                width="24"
                height="24"
                style={{ color: theme.palette.grey[900] }}
              />
              {/* <RightIcon isRTL={isRTL} /> */}
            </StyledIconButton>
          )}
        </Box>
      </ContentContainer>
    </StyledCard>
  );
};

export default ProductCard;
