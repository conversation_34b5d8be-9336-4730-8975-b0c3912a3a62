import { styled, Theme } from '@mui/material/styles';
import { Box, Card, IconButton, Button, Typography } from '@mui/material';

export const StyledCard = styled(Card)(({ theme }) => ({
  display: 'grid',
  gridTemplateRows: 'auto 1fr',
  justifySelf: 'end',
  maxWidth: '316px',
  width: '100%',
  height: '581px',
  aspectRatio: '316 / 581',
  borderRadius: 0,
  overflow: 'hidden',

  [theme.breakpoints.down('md')]: {
    maxWidth: '280px',
    height: '520px',
    aspectRatio: '280 / 520',
  },
  [theme.breakpoints.down('sm')]: {
    maxWidth: '220px',
    height: '410px',
    aspectRatio: '220 / 410',
  },
}));

export const StyledCardTitle = styled(Typography)(({ theme }) => ({
  color: '#333',
  fontWeight: 'bold',
  maxHeight: '3.6em',
  lineHeight: '1.5em',
  overflow: 'hidden',
  textOverflow: 'ellipsis',
  display: '-webkit-box',
  WebkitLineClamp: 2,
  WebkitBoxOrient: 'vertical',
}));

export const ImageContainer = styled(Box)(({ theme }) => ({
  position: 'relative',
  height: '384px',
  width: '100%',
  aspectRatio: '316 / 384',
  overflow: 'hidden',

  [theme.breakpoints.down('md')]: {
    height: '330px',
    aspectRatio: '316 / 384',
  },
  [theme.breakpoints.down('sm')]: {
    height: '280px',
    aspectRatio: '316 / 384',
  },

  '&:hover': {
    '& img': {
      transform: 'scale(1.1)',
    },
    '& .cart-overlay': {
      opacity: 1,
    },
    '& .add-to-cart-button': {
      transform: 'translateY(-18px)',
    },
  },
}));

export const ProductImage = styled('img')({
  width: '100%',
  height: '100%',
  objectFit: 'cover',
  transition: 'transform 0.3s ease-in-out',
});

export const WishlistButton = styled(IconButton)(
  ({ theme, isFavorite }: { theme: Theme; isFavorite: boolean }) => ({
    position: 'absolute',
    top: 8,
    left: 8,
    width: 59,
    height: 59,
    background: isFavorite ? theme.palette.secondary.main : '#0003',
    '&:hover': {
      background: theme.palette.secondary.main,
    },
  })
);

export const CartOverlay = styled(Box)({
  position: 'absolute',
  bottom: 0,
  left: 0,
  right: 0,
  height: '20%',
  background: '#0003',
  opacity: 0,
  transition: 'opacity 0.3s ease-in-out',
});

export const AddToCartButton = styled(Button)(({ theme }) => ({
  position: 'absolute',
  bottom: 0,
  left: 16,
  right: 16,
  backgroundColor: theme.palette.primary.main,
  color: theme.palette.common.white,
  borderRadius: 20,
  transform: 'translateY(100%)',
  transition: 'transform 0.3s ease-in-out',

  '&:hover': {
    backgroundColor: theme.palette.primary.main,
  },
}));

export const ContentContainer = styled(Box)(({ theme }) => ({
  display: 'flex',
  flexDirection: 'column',
  justifyContent: 'space-between',
  padding: 16,
  direction: theme.direction,
  gap: 8,
  minHeight: 0,
}));
