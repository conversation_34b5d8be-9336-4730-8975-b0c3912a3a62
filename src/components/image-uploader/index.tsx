import { useState, useRef } from 'react';
import {
  <PERSON>,
  Dialog,
  DialogTitle,
  DialogContent,
  <PERSON>alogActions,
  Button,
  Slider,
} from '@mui/material';
import { Icon } from '@iconify/react';

interface ImageUploadProps {
  onImageChange: (image: string) => void;
  currentImage: string;
}

const ImageUpload = ({ onImageChange, currentImage }: ImageUploadProps) => {
  const [open, setOpen] = useState(false);
  const [image, setImage] = useState<string | null>(null);
  const [scale, setScale] = useState(1);
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });

  const imageRef = useRef<HTMLImageElement>(null);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const reader = new FileReader();
      reader.onload = () => {
        setImage(reader.result as string);
        setOpen(true);
        setScale(1);
        setPosition({ x: 0, y: 0 });
      };
      reader.readAsDataURL(e.target.files[0]);
    }
  };

  const handleMouseDown = (e: React.MouseEvent) => {
    setIsDragging(true);
    setDragStart({
      x: e.clientX - position.x,
      y: e.clientY - position.y,
    });
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    if (isDragging) {
      setPosition({
        x: e.clientX - dragStart.x,
        y: e.clientY - dragStart.y,
      });
    }
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  const handleSave = () => {
    if (imageRef.current) {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');

      // Set canvas size to desired output size (150x150)
      canvas.width = 150;
      canvas.height = 150;

      if (ctx && image) {
        // Draw the image with current scale and position
        const img = imageRef.current;

        // Create circular clip path
        ctx.beginPath();
        ctx.arc(75, 75, 75, 0, Math.PI * 2);
        ctx.clip();

        // Draw image with transformations
        ctx.drawImage(
          img,
          -position.x / scale,
          -position.y / scale,
          img.width,
          img.height,
          0,
          0,
          150,
          150
        );

        // Convert to base64 and update
        const croppedImage = canvas.toDataURL('image/jpeg');
        onImageChange(croppedImage);
      }
    }
    setOpen(false);
  };

  return (
    <>
      <Box
        sx={{
          position: 'relative',
          width: 150,
          height: 150,
          borderRadius: '50%',
          overflow: 'hidden',
          cursor: 'pointer',
        }}
        onClick={() => document.getElementById('image-upload')?.click()}
      >
        <img
          src={currentImage}
          alt="Profile"
          style={{ width: '100%', height: '100%', objectFit: 'cover' }}
        />
        <Box
          sx={{
            position: 'absolute',
            bottom: 0,
            left: 0,
            right: 0,
            bgcolor: 'rgba(0,0,0,0.5)',
            color: 'white',
            textAlign: 'center',
            py: 1,
          }}
        >
          <Icon icon="mdi:camera" width={24} height={24} />
        </Box>
      </Box>

      <input type="file" id="image-upload" hidden accept="image/*" onChange={handleFileChange} />

      <Dialog open={open} onClose={() => setOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>تعديل الصورة</DialogTitle>
        <DialogContent>
          <Box
            sx={{
              position: 'relative',
              width: '100%',
              height: 400,
              overflow: 'hidden',
              cursor: isDragging ? 'grabbing' : 'grab',
            }}
            onMouseDown={handleMouseDown}
            onMouseMove={handleMouseMove}
            onMouseUp={handleMouseUp}
            onMouseLeave={handleMouseUp}
          >
            {image && (
              <Box
                sx={{
                  position: 'absolute',
                  top: '50%',
                  left: '50%',
                  transform: `translate(-50%, -50%) translate(${position.x}px, ${position.y}px)`,
                  width: '100%',
                  height: '100%',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}
              >
                <img
                  ref={imageRef}
                  src={image}
                  alt="Crop preview"
                  style={{
                    transform: `scale(${scale})`,
                    maxWidth: '100%',
                    maxHeight: '100%',
                  }}
                  draggable={false}
                />
              </Box>
            )}
            <Box
              sx={{
                position: 'absolute',
                top: '50%',
                left: '50%',
                transform: 'translate(-50%, -50%)',
                width: 150,
                height: 150,
                borderRadius: '50%',
                border: '2px solid white',
                boxShadow: '0 0 0 9999px rgba(0, 0, 0, 0.5)',
                pointerEvents: 'none',
              }}
            />
          </Box>
          <Box sx={{ mt: 2, px: 3 }}>
            <Slider
              value={scale}
              onChange={(_, value) => setScale(value as number)}
              min={0.5}
              max={3}
              step={0.1}
              aria-label="Zoom"
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpen(false)}>إلغاء</Button>
          <Button onClick={handleSave} variant="contained">
            حفظ
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default ImageUpload;
