import React, { useState, useEffect } from 'react';
import { Fab } from '@mui/material';
import Iconify from '../iconify';

function ScrollToTopButton() {
  const [isVisible, setIsVisible] = useState(false);

  // Show button when page is scrolled down
  const toggleVisibility = () => {
    if (window.pageYOffset > 100) {
      setIsVisible(true);
    } else {
      setIsVisible(false);
    }
  };

  // Scroll the window to the top smoothly
  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth',
    });
  };

  useEffect(() => {
    window.addEventListener('scroll', toggleVisibility);
    return () => {
      window.removeEventListener('scroll', toggleVisibility);
    };
  }, []);

  return (
    <div>
      {isVisible && (
        <Fab
          color="secondary"
          aria-label="scroll-to-top"
          onClick={scrollToTop}
          sx={{
            position: 'fixed',
            width: '40px',
            height: '40px',
            bottom: 40,
            left: 40,
            zIndex: 1000,
            '&:hover': {
              backgroundColor: 'primary.dark',
            },
          }}
        >
          <Iconify icon="tabler:arrow-up" />
        </Fab>
      )}
    </div>
  );
}

export default ScrollToTopButton;
