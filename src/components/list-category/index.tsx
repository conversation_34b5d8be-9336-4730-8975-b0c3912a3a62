import { <PERSON>, But<PERSON>, Divider, Stack, Typography } from '@mui/material';
import { useTheme } from '@mui/material/styles';

import React from 'react';
import { useRouter } from 'next/navigation';
import Carousel, { CarouselArrowIndex, useCarousel } from 'src/components/carousel';
// import ProductItem from 'src/components/product-card-item';
import { paths } from 'src/routes/paths';
import { ProductsByCategory } from 'src/types/home';
import { useLocales } from 'src/locales';
import ProductCard from '../product-card-item';

function ListHomeCategory({ category }: { category: ProductsByCategory }) {
  const { t } = useLocales();
  const { category_name, products, category_id } = category;
  const theme = useTheme();
  const router = useRouter();

  const isRTL = theme.direction === 'rtl';

  const carousel = useCarousel({
    rtl: isRTL,
    draggable: false,
    adaptiveHeight: true,
    slidesToShow: 5,

    responsive: [
      {
        breakpoint: 1800,
        settings: { slidesToShow: 5 },
      },
      {
        breakpoint: 1600,
        settings: { slidesToShow: 4 },
      },
      {
        breakpoint: 1500,
        settings: { slidesToShow: 3.5 },
      },
      {
        breakpoint: 1400,
        settings: { slidesToShow: 3 },
      },
      {
        breakpoint: 1200,
        settings: { slidesToShow: 2.5 },
      },
      {
        breakpoint: 1000,
        settings: { slidesToShow: 2 },
      },
      {
        breakpoint: 750,
        settings: { slidesToShow: 1.5, centerPadding: '0' },
      },
      {
        breakpoint: 400,
        settings: { slidesToShow: 1.3, centerPadding: '0' },
      },
    ],
  });

  const handleShowAll = () => {
    router.push(paths.category(`${category_id}`));
  };

  return (
    <Stack spacing={4}>
      <Stack
        sx={{ alignContent: 'center', justifyContent: 'space-between' }}
        direction="row"
        gap="34px"
        divider={
          <Divider
            sx={{
              flexGrow: 1,
              borderColor: theme.palette.primary.dark,
              alignSelf: 'center',
              '@media (max-width:500px)': {
                display: 'none',
              },
            }}
          />
        }
      >
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'row',
            spacing: '2',
            alignItems: 'center',
            gap: '34px',
          }}
        >
          <Typography
            variant="h3"
            sx={{
              whiteSpace: 'nowrap',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              color: theme.palette.primary.dark,
              '@media (max-width:500px)': {
                fontSize: '1.2rem',
                maxWidth: '180px',
              },
            }}
          >
            {category_name}
          </Typography>
        </Box>
        <Stack direction="row" spacing={2} alignItems="center">
          <CarouselArrowIndex
            index={carousel.currentIndex}
            total={products?.length || 0}
            onNext={carousel.onNext}
            onPrev={carousel.onPrev}
            sx={{
              display: 'flex',
              gap: '4',
              '@media (max-width:500px)': {
                display: 'none',
              },
            }}
          />

          <Button
            onClick={handleShowAll}
            variant="outlined"
            sx={{
              border: () => `1px solid ${theme.palette.primary.dark}`,
              color: theme.palette.primary.dark,
              width: '103px',
              height: '47px',
              borderRadius: '24px',
              '&:hover': {
                border: () => `1px solid ${theme.palette.primary.main}`,
                color: () => theme.palette.primary.main,
                bgcolor: 'transparent',
              },
              '@media (max-width:500px)': {
                fontSize: '0.8rem',
                width: '80px',
                height: '40px',
              },
            }}
          >
            {t('product_details.show_all')}
          </Button>
        </Stack>
      </Stack>

      <Box
        sx={{
          p: 0,
          overflow: 'hidden',
          position: 'relative',
        }}
      >
        {!products?.length ? (
          <Box sx={{ textAlign: 'center', py: 4 }}>
            <Typography variant="h6" color="text.secondary">
              لا توجد منتجات في هذه الفئة حالياً، يرجى المحاولة مرة أخرى لاحقاً
            </Typography>
          </Box>
        ) : (
          <Carousel ref={carousel.carouselRef} {...carousel.carouselSettings}>
            {products!!.map((product) => (
              <ProductCard category={product.name} key={product.id} product={product} />
            ))}
          </Carousel>
        )}
      </Box>
    </Stack>
  );
}

export default ListHomeCategory;
