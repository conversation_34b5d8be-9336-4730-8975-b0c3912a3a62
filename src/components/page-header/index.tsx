'use client';

import { Box, Breadcrumbs, Divider, <PERSON>, Typo<PERSON> } from '@mui/material';
import HomeIcon from 'src/assets/icons/HomeIcon';
import { useLocales } from 'src/locales';
import { Header } from './types';

export default function PageHeader({ title, breadcrumbsPath }: Header) {
  const { t } = useLocales();
  return (
    <Box sx={{ mb: '31px' }}>
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
          width: '100%',
        }}
      >
        <Typography
          variant="h2"
          sx={{
            fontSize: {
              xs: '1rem',
              sm: '2rem',
              md: '2.5rem',
            },
          }}
        >
          {title}
        </Typography>
        {/* Breadcrumbs */}
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            gap: '10px',
          }}
        >
          <HomeIcon />
          <Breadcrumbs
            aria-label="breadcrumb"
            sx={{
              display: 'flex',
              pt: '6px',
            }}
          >
            <Link
              underline="hover"
              color="inherit"
              href="/"
              sx={{
                fontSize: {
                  xs: '1rem',
                  sm: '2rem',
                  md: '2.5rem',
                },
              }}
            >
              {t('common.home')}
            </Link>
            {breadcrumbsPath &&
              breadcrumbsPath.map(({ name, path }) => (
                <Link
                  underline="hover"
                  color="inherit"
                  href={`/${path}`}
                  key={path}
                  sx={{
                    fontSize: {
                      xs: '1rem',
                      sm: '2rem',
                      md: '2.5rem',
                    },
                  }}
                >
                  {name}
                </Link>
              ))}
            <Typography color="text.primary">{title}</Typography>
          </Breadcrumbs>
        </Box>
      </Box>
      <Divider sx={{ borderColor: '#BEBEBE', width: '100%' }} />
    </Box>
  );
}
