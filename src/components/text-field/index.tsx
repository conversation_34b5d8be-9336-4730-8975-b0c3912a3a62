import { Box, Typography } from '@mui/material';
import { StyledTextField } from './styles';
import { ITextField } from './types';

const InputTextField = ({
  value,
  name,
  error,
  label,
  helperText,
  placeholder,
  maxWidth,
  maxHeight,
  onChange,
}: ITextField) => (
  <Box
    sx={{
      display: 'flex',
      flexDirection: 'column',
      width: '100%',
      maxWidth,
    }}
  >
    <Typography variant="subtitle1" sx={{ mb: 1 }}>
      {label}
    </Typography>
    <StyledTextField
      name={name}
      value={value}
      error={error}
      onChange={onChange}
      helperText={helperText}
      placeholder={placeholder}
      sx={{
        maxHeight: { maxHeight },
        maxWidth: { maxWidth },
      }}
    />
  </Box>
);

export default InputTextField;
