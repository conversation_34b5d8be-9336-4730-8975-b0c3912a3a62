//
import { useTheme } from '@mui/material/styles';
import Iconify, { IconifyProps } from '../iconify';

// ----------------------------------------------------------------------

type Props = {
  icon?: IconifyProps; // Right icon
  isRTL?: boolean;
};

export function LeftIcon({ icon = 'eva:arrow-ios-forward-fill', isRTL }: Props) {
  const theme = useTheme();

  return (
    <Iconify
      icon={icon}
      sx={{
        width: '47px',
        height: '47px',
        padding: '10px',
        color: theme.palette.primary.dark,
        transform: isRTL ? 'scaleX(1)' : 'scaleX(-1)',
        '&:hover': {
          opacity: 1,
          color: theme.palette.primary.main,
        },
      }}
    />
  );
}

export function RightIcon({ icon = 'eva:arrow-ios-forward-fill', isRTL }: Props) {
  const theme = useTheme();
  return (
    <Iconify
      icon={icon}
      sx={{
        width: '47px',
        height: '47px',
        padding: '10px',
        color: theme.palette.primary.dark,
        transform: isRTL ? 'rotateY(180deg)' : 'none',
        '&:hover': {
          opacity: 1,
          color: theme.palette.primary.main,
        },
      }}
    />
  );
}
