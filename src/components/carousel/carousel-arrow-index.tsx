// @mui
import { useTheme, styled, Theme, SxProps } from '@mui/material/styles';
import Box from '@mui/material/Box';
import IconButton from '@mui/material/IconButton';
//
import { IconifyProps } from '../iconify';
//
import { LeftIcon, RightIcon } from './arrow-icons';

// ----------------------------------------------------------------------

const StyledRoot = styled(Box)(({ theme }) => ({
  zIndex: 9,
  display: 'inline-flex',
  alignItems: 'center',
  gap: '8px',
  padding: theme.spacing(0.25),
  color: theme.palette.primary.main,
  borderRadius: theme.shape.borderRadius,
}));

export const StyledIconButton = styled(IconButton)(({ theme }) => ({
  width: 47,
  height: 47,
  padding: 0,
  border: `1px solid ${theme.palette.primary.dark}`,
  borderRadius: '100%',
  '&:hover': {
    opacity: 1,
    border: `1px solid ${theme.palette.primary.main}`,
  },
}));

// ----------------------------------------------------------------------

type Props = {
  index: number;
  total: number;
  icon?: IconifyProps; // Right icon
  onNext?: VoidFunction;
  onPrev?: VoidFunction;
  sx?: SxProps<Theme>;
};

export default function CarouselArrowIndex({
  index,
  total,
  onNext,
  onPrev,
  icon,
  sx,
  ...other
}: Props) {
  const theme = useTheme();

  const isRTL = theme.direction === 'rtl';

  return (
    <Box position="relative">
      <StyledRoot sx={sx} {...other}>
        <StyledIconButton onClick={onPrev}>
          <LeftIcon icon={icon} isRTL={isRTL} />
        </StyledIconButton>

        <StyledIconButton onClick={onNext}>
          <RightIcon icon={icon} isRTL={isRTL} />
        </StyledIconButton>
      </StyledRoot>
    </Box>
  );
}
