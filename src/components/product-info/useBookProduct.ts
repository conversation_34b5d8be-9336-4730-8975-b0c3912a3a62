import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { enqueueSnackbar } from 'notistack';
import { paths } from 'src/routes/paths';
import { useCartStore } from 'src/store/user/useCartStore';
import { useToggleFavorite } from 'src/api/product';
import { useCartAPIRequest } from 'src/api/cart';
import { IProductItem } from 'src/types/product';
import { useLocales } from 'src/locales';
import { useAuthContext } from 'src/auth/hooks/use-auth-context';

export const useBookProduct = () => {
  const { authenticated } = useAuthContext();
  const { t } = useLocales();
  const router = useRouter();
  const { handleToggleFavorite } = useToggleFavorite();
  const [quantity, setQuantity] = useState(1);
  const [wishlist, setWishlist] = useState(false);
  const { addBook } = useCartStore();
  const { handleAddToCart: handleAddToCartAPI } = useCartAPIRequest();
  const handleIncrement = () => {
    setQuantity((prev) => prev + 1);
  };

  const handleDecrement = () => {
    setQuantity((prev) => Math.max(1, prev - 1));
  };

  const handleAddToCart = async (book: IProductItem) => {
    try {
      if (!authenticated) {
        enqueueSnackbar(t('product_card.please_login'), { variant: 'error' });
        return;
      }
      const response = await handleAddToCartAPI(book.id, quantity);
      if (response.success) {
        addBook(book.id, quantity, book.price_for_selling);
        enqueueSnackbar(t('cart.item_added'), { variant: 'success' });
      } else {
        enqueueSnackbar(t('cart.error_adding_item'), { variant: 'error' });
      }
    } catch (error) {
      enqueueSnackbar(t('product_card.unexpected_error'), { variant: 'error' });
      console.error('Error adding to cart:', error);
    }
  };

  const HandleBuyNow = () => {
    // Implement buy now functionality
    try {
      if (!authenticated) {
        enqueueSnackbar(t('product_card.please_login'), { variant: 'error' });
        return;
      }
      router.push(paths.payment);
    } catch (error) {
      enqueueSnackbar(t('product_card.unexpected_error'), { variant: 'error' });
      console.error('Error buying now:', error);
    }
  };

  const handleAddToWishlist = async (id: number) => {
    // Implement wishlist functionality
    try {
      if (!authenticated) {
        enqueueSnackbar(t('product_card.please_login'), { variant: 'error' });
        return;
      }
      const response = await handleToggleFavorite(id);
      if (response.success) {
        setWishlist(!wishlist);
        if (wishlist) {
          enqueueSnackbar(t('profile.favourite.item_removed'), { variant: 'success' });
        } else {
          enqueueSnackbar(t('product_card.added_to_wishlist_success'), { variant: 'success' });
        }
      } else {
        enqueueSnackbar(t('product_card.error_adding_to_wishlist'), { variant: 'error' });
      }
    } catch (error) {
      enqueueSnackbar(t('product_card.unexpected_error_adding_to_wishlist'), { variant: 'error' });
      console.error('Error adding to wishlist:', error);
    }
  };

  return {
    quantity,
    wishlist,
    handleIncrement,
    handleDecrement,
    handleAddToCart,
    HandleBuyNow,
    handleAddToWishlist,
  };
};
