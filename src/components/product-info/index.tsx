import { Box, Card } from '@mui/material';
import BookGallery from './book-gallery';
import BookInfo from './book-info';
import BookActions from './book-actions';
import { useBookProduct } from './useBookProduct';
import { Props } from './types';

const BookProduct = ({ gallery, book, rating, price, title, author }: Props) => {
  const {
    quantity,
    wishlist,
    handleIncrement,
    handleDecrement,
    handleAddToCart,
    HandleBuyNow,
    handleAddToWishlist,
  } = useBookProduct();

  return (
    <Card
      sx={{
        background: 'background.paper',
        borderRadius: '30px',
        p: '0',
        height: '100%',
        width: '100%',
      }}
    >
      <Box
        sx={{
          display: 'flex',
          gap: '72px',
          flexFlow: 'row wrap',
          '@media (max-width: 1350px)': {
            gap: '8px',
            width: '100%',
            justifyContent: 'center',
            alignItems: 'center',
          },
          '@media (max-width: 800px)': {
            justifyContent: 'center',
          },
        }}
      >
        <BookGallery gallery={gallery} />
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            gap: 3,
            marginTop: '100px',
            marginBottom: '82px',
            width: '100%',
            maxWidth: '597px',
            maxHeight: '504px',
            '@media (max-width: 1520px)': {
              gap: '8px',
              width: '80%',
              justifyContent: 'center',
              alignItems: 'center',
            },
            '@media (max-width: 1310px)': {
              marginTop: '8px',
              mx: '37px',
            },
          }}
        >
          <BookInfo
            title={title || 'Book Title'}
            author={author || 'Book Author'}
            rating={rating || 0}
            price={price || 0}
            details={{
              publisher: 'مركز دار المنهاج للدراسات والتحقيق العلمي',
              subject: 'علم الكلام',
              releaseYear: '2023 م - 1444 هـ',
              edition: 'الأول',
            }}
          />
          <BookActions
            quantity={quantity}
            wishlist={wishlist}
            onIncrement={handleIncrement}
            onDecrement={handleDecrement}
            onAddToCart={() => handleAddToCart(book!)}
            onBuyNow={HandleBuyNow}
            onAddToWishlist={() => handleAddToWishlist(book!.id)}
          />
        </Box>
      </Box>
    </Card>
  );
};

export default BookProduct;
