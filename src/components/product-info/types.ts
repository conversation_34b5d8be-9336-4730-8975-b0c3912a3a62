import { IProductItem } from 'src/types/product';

export interface IBookActionsProps {
  quantity: number;
  wishlist: boolean;
  onIncrement: () => void;
  onDecrement: () => void;
  onAddToCart: () => void;
  onBuyNow: () => void;
  onAddToWishlist: () => void;
}

export interface IBookDetails {
  publisher: string;
  subject: string;
  releaseYear: string;
  edition: string;
}

export interface IBookInfoProps {
  title: string;
  author: string;
  rating: number;
  price: number;
  details: IBookDetails;
}

export type Thumbnail = {
  url: string;
  alt?: string;
};

export type Props = {
  id?: number;
  gallery: Thumbnail[];
  book?: IProductItem;
  rating?: number;
  price?: number;
  title?: string;
  author?: string;
};
