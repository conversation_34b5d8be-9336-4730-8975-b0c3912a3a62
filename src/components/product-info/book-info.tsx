import { Box, Typography, Rating, Tooltip } from '@mui/material';
import { fCurrency } from 'src/utils/format-number';
import { useLocales } from 'src/locales';
import { IBookInfoProps } from './types';
import Iconify from '../iconify';

const BookInfo = ({ title, author, rating, price, details }: IBookInfoProps) => {
  const { t } = useLocales();
  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        gap: 2,
        width: '100%',
        maxWidth: '597px',
      }}
    >
      <Typography variant="h4" component="h1" fontWeight="bold">
        {title}
      </Typography>

      <Typography variant="h5" color="text.secondary">
        {author}
      </Typography>

      <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
        <Typography
          sx={{
            display: 'flex',
            gap: 1,
            alignContent: 'center',
            justifyContent: 'center',
            width: '92px',
          }}
          variant="h5"
          color="primary"
          fontWeight="bold"
        >
          <Iconify
            icon="mdi:tag-outline"
            width={30}
            height={30}
            sx={{ color: (theme) => theme.palette.primary.main }}
          />
          {fCurrency(price)}
        </Typography>
        <Tooltip title={`${(rating * 20).toFixed(2)}%`}>
          <Box>
            <Rating
              value={rating}
              readOnly
              size="small"
              sx={{
                '& .MuiRating-icon': {
                  width: 18,
                  height: 18,
                  marginRight: 1,
                },
              }}
            />
          </Box>
        </Tooltip>
      </Box>

      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
        <Typography color="text.secondary">
          {t('product_details.publisher')}: {details.publisher}
        </Typography>
        <Typography color="text.secondary">
          {t('product_details.subject')}: {details.subject}
        </Typography>
        <Typography color="text.secondary">
          {t('product_details.release_year')}: {details.releaseYear}
        </Typography>
        <Typography color="text.secondary">
          {t('product_details.edition')}: {details.edition}
        </Typography>
      </Box>
    </Box>
  );
};

export default BookInfo;
