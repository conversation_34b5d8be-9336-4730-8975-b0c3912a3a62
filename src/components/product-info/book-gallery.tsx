import { useState } from 'react';
import { Box, ImageList, ImageListItem } from '@mui/material';
import { useTheme } from '@mui/material/styles';
import useMediaQuery from '@mui/material/useMediaQuery';
import Image from 'src/components/image';
import Carousel, { useCarousel } from 'src/components/carousel';

type Props = {
  gallery: {
    url: string;
    alt?: string;
  }[];
};

const BookGallery = ({ gallery }: Props) => {
  const theme = useTheme();
  // Consider screen as mobile when width is less than 800px
  const isMobile = useMediaQuery('(max-width:800px)');
  const [selectedImage, setSelectedImage] = useState(gallery[0]);

  const carousel = useCarousel({
    speed: 800,
    rtl: theme.direction === 'rtl',
  });

  return (
    <Box
      sx={{
        direction: 'rtl',
        display: 'flex',
        mx: '37px',
        width: '100%',
        maxWidth: '600px',
        maxHeight: '624px',
        gap: '16px',
        p: 0,
        my: '31px',
        '@media (max-width: 1024px)': {
          mt: '31px',
          mb: '0px',
        },
        '@media (max-width: 800px)': {
          flexDirection: 'column',
          alignItems: 'center',
          gap: '8px',
        },
      }}
    >
      {isMobile ? (
        <Box
          sx={{
            position: 'relative',
            width: '90%',
            maxWidth: '477px',
            maxHeight: '624px',
            overflow: 'hidden',
            margin: '0 auto',
            '@media (max-width: 800px)': {
              maxWidth: '100%',
              maxHeight: 'auto',
            },
          }}
        >
          <Carousel ref={carousel.carouselRef} {...carousel.carouselSettings}>
            {gallery.map((img, index) => (
              <Box
                key={index}
                sx={{
                  position: 'relative',
                  width: '100%',
                  height: '100%',
                }}
              >
                <Image src={img.url} alt={img.alt ?? 'Book cover'} style={{ objectFit: 'cover' }} />
              </Box>
            ))}
          </Carousel>
        </Box>
      ) : (
        <Box
          sx={{
            position: 'relative',
            width: '100%',
            maxWidth: '477px',
            maxHeight: '624px',
            aspectRatio: '1/1',
            overflow: 'hidden',
            margin: '0 auto',
            '@media (max-width: 800px)': {
              maxWidth: '100%',
              maxHeight: 'auto',
            },
          }}
        >
          <Image
            src={selectedImage.url}
            alt={selectedImage.alt ?? 'Book cover'}
            style={{ objectFit: 'cover' }}
          />
        </Box>
      )}

      {/* Thumbnails – hidden on mobile */}
      {!isMobile && (
        <ImageList
          sx={{
            display: 'flex',
            flexDirection: 'column', // Thumbnails stacked vertically on large screens
            justifyContent: 'space-between',
            m: 0,
            maxWidth: '112px',
            width: '100%',
            '@media (max-width: 800px)': {
              display: 'none', // Hide thumbnails on mobile
            },
          }}
          cols={1}
        >
          {gallery.map((thumb, index) => (
            <ImageListItem
              key={index}
              sx={{
                cursor: 'pointer',
                overflow: 'hidden',
                border: 2,
                borderColor: selectedImage === thumb ? 'primary.main' : 'transparent',
                maxWidth: '112px',
                height: '146px',
              }}
              onClick={() => setSelectedImage(thumb)}
            >
              <Image
                src={thumb.url}
                alt={thumb.alt ?? `Thumbnail ${index + 1}`}
                width={112}
                height={146}
                style={{ objectFit: 'cover' }}
              />
            </ImageListItem>
          ))}
        </ImageList>
      )}
    </Box>
  );
};

export default BookGallery;
