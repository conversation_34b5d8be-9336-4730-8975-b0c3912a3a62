import { Box, IconButton, Typography } from '@mui/material';
import { Icon } from '@iconify/react';
import { useTheme } from '@mui/material/styles';
import { useLocales } from 'src/locales';
import QuantitySelector from '../../layouts/product-details/quantity-selector';
import CustomButton from '../../layouts/_common/button-detail';
import { IBookActionsProps } from './types';

const BookActions = ({
  quantity,
  wishlist,
  onIncrement,
  onDecrement,
  onAddToCart,
  onBuyNow,
  onAddToWishlist,
}: IBookActionsProps) => {
  const { t } = useLocales();
  const theme = useTheme();

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, width: '100%' }}>
      {/* Quantity Selector Row */}
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
        <Typography color="text.secondary" sx={{ direction: 'rtl' }}>
          {t('product_details.quantity')}
        </Typography>
        <QuantitySelector onIncrement={onIncrement} onDecrement={onDecrement}>
          {quantity}
        </QuantitySelector>
      </Box>

      {/* Buttons Container: column on xs/sm, row on md+ */}
      <Box
        sx={{
          display: 'flex',
          flexDirection: { xs: 'column', md: 'row' },
          gap: 2,
          width: '100%',
        }}
      >
        {/* Wishlist Button */}
        <IconButton
          onClick={onAddToWishlist}
          sx={{
            display: { xs: 'none', md: 'flex' },
            height: '42px',
            width: { xs: '100%', md: '42px' },
            background: 'transparent',
            p: 1,
          }}
          color="primary"
        >
          <Icon
            icon={wishlist ? 'mdi:heart' : 'material-symbols:favorite-outline'}
            width="42px"
            height="42px"
            color={wishlist ? theme.palette.secondary.main : theme.palette.primary.dark}
          />
        </IconButton>
        <CustomButton
          sx={{
            display: { xs: 'flex', md: 'none' },
            alignItems: 'center',
            gap: '8px',
            height: '47px',
            width: { xs: '100%', md: 'auto' },
            bgcolor: theme.palette.primary.dark,
            '&:hover': {
              color: theme.palette.common.white,
              bgcolor: theme.palette.primary.main,
            },
          }}
          variant="contained"
          onClick={onAddToWishlist}
        >
          <Icon
            icon={wishlist ? 'mdi:heart' : 'material-symbols:favorite-outline'}
            width="22px"
            height="22px"
            color={theme.palette.secondary.main}
          />
          <Typography
            color={theme.palette.common.white}
            sx={{
              fontSize: '1rem',
              fontWeight: 'bold',
            }}
          >
            {wishlist
              ? t('product_details.remove_from_favourite')
              : t('product_details.add_to_favourite')}
          </Typography>
        </CustomButton>
        {/* Add To Cart Button */}
        <CustomButton
          sx={{
            display: 'flex',
            alignItems: 'center',
            gap: '8px',
            height: '47px',
            width: { xs: '100%', md: 'auto' },
            bgcolor: theme.palette.primary.dark,
            '&:hover': {
              color: theme.palette.common.white,
              bgcolor: theme.palette.primary.main,
            },
          }}
          variant="contained"
          onClick={onAddToCart}
        >
          <Icon
            color={theme.palette.common.white}
            icon="material-symbols:shopping-cart-outline"
            width="22px"
            height="22px"
          />
          <Typography
            color={theme.palette.common.white}
            sx={{
              fontSize: '1rem',
              fontWeight: 'bold',
            }}
          >
            {t('product_details.add_to_cart')}
          </Typography>
        </CustomButton>
        {/* Buy Now Button */}
        <CustomButton
          sx={{
            display: 'flex',
            alignItems: 'center',
            gap: '8px',
            height: '47px',
            width: { xs: '100%', md: 'auto' },
            bgcolor: theme.palette.secondary.main,
            '&:hover': {
              color: theme.palette.common.white,
              bgcolor: theme.palette.primary.main,
            },
          }}
          variant="contained"
          onClick={onBuyNow}
        >
          <Icon
            color={theme.palette.common.white}
            icon="mdi-light:credit-card"
            width="22px"
            height="22px"
          />
          <Typography
            color={theme.palette.common.white}
            sx={{
              fontSize: '1rem',
              fontWeight: 'bold',
            }}
          >
            {t('product_details.buy_now')}
          </Typography>
        </CustomButton>
      </Box>
    </Box>
  );
};

export default BookActions;
