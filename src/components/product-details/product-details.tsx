import { Card, CardContent, Typography, Grid } from '@mui/material';
import { useLocales } from 'src/locales';
import ProductHeader from './product-header';
import ProductSpecification from './product-specification';
import type { ProductDetailsType } from './types';

const ProductDetails = ({ productData }: { productData: ProductDetailsType }) => {
  const { t } = useLocales();
  const { subtitle, author, rating, specifications } = productData;

  const keyTranslations: { [key: string]: string } = {
    weight: t('product_details.weight'),
    packaging: t('product_details.packaging'),
    material: t('product_details.material'),
    pages: t('product_details.pages'),
    dimensions: t('product_details.dimensions'),
  };

  return (
    <Card
      sx={{
        bgcolor: 'background.paper',
        boxShadow: (theme) => theme.shadows[1],
        borderRadius: '30px',
      }}
    >
      <CardContent sx={{ p: 3 }}>
        <Typography variant="h3" component="h2" fontWeight="bold" sx={{ mb: 1 }}>
          {t('product_details.product_details')}
        </Typography>
        <ProductHeader subtitle={subtitle} author={author} rating={rating} />
        <Grid container spacing={2}>
          {Object.entries(specifications).map(([key, value]) => (
            <Grid key={key} item xs={12} sm={6} md={3}>
              <ProductSpecification label={keyTranslations[key]} value={value} />
            </Grid>
          ))}
        </Grid>
      </CardContent>
    </Card>
  );
};

export default ProductDetails;
