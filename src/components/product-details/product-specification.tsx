import { Box, Typography } from '@mui/material';

interface ProductSpecificationProps {
  label: string;
  value: string;
}

const ProductSpecification = ({ label, value }: ProductSpecificationProps) => (
  <Box
    sx={{
      display: 'flex',
      alignItems: 'center',
      py: 1.5,
      gap: '15px',
    }}
  >
    <Typography fontWeight="medium" fontSize="1.2rem">
      {label}
    </Typography>
    <Typography color="text.secondary" fontSize="1.2rem">
      {value}
    </Typography>
  </Box>
);

export default ProductSpecification;
