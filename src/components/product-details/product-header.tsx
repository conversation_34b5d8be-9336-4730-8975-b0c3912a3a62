import { Box, Typography } from '@mui/material';
import { useLocales } from 'src/locales';
import StarRating from '../product-reviews/five-star-rating';

interface ProductHeaderProps {
  subtitle: string;
  author: string;
  rating: number;
}

const ProductHeader = ({ subtitle, author, rating }: ProductHeaderProps) => {
  const { t } = useLocales();
  return (
    <Box sx={{ mb: 3 }}>
      <Box style={{ display: 'flex', gap: '4px' }}>
        <Typography sx={{ mb: 0.5, fontWeight: 'medium', fontSize: '1.2rem' }}>
          {t('product_details.publisher')}:
        </Typography>
        <Typography sx={{ mb: 0.5, color: 'text.secondary', fontSize: '1.2rem' }}>
          {subtitle}
        </Typography>
      </Box>
      <Box sx={{ display: 'flex', gap: '30px' }}>
        <Typography sx={{ mb: 1, color: 'text.secondary', margin: '0px', fontSize: '1rem' }}>
          {author}
        </Typography>
        {/* !!TODO: Star component */}
        {/* <StarRating rating={rating} /> */}
        <StarRating rating={rating} disabled width="17px" height="17px" />
      </Box>
    </Box>
  );
};

export default ProductHeader;
