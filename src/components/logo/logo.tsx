import { forwardRef } from 'react';
// @mui
// import { useTheme } from '@mui/material/styles';
import Link from '@mui/material/Link';
import Box, { BoxProps } from '@mui/material/Box';
// routes
import { RouterLink } from 'src/routes/components';

// ----------------------------------------------------------------------

export interface LogoProps extends BoxProps {
  disabledLink?: boolean;
  color?: string;
}

const Logo = forwardRef<HTMLDivElement, LogoProps>(
  ({ disabledLink = false, color, sx, ...other }, ref) => {
    // const theme = useTheme();

    // const PRIMARY_LIGHT = theme.palette.primary.light;

    // const PRIMARY_MAIN = theme.palette.primary.main;

    // const PRIMARY_DARK = theme.palette.primary.dark;

    // OR using local (public folder)
    // -------------------------------------------------------
    // const logo = (
    //   <Box
    //     component="img"
    //     src="/logo/logo_single.svg" => your path
    //     sx={{ width: 40, height: 40, cursor: 'pointer', ...sx }}
    //   />
    // );

    const logo = (
      <Box
        ref={ref}
        component="div"
        sx={{
          width: 94,
          display: 'inline-flex',
          ...sx,
        }}
        {...other}
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="93.389"
          height="82.884"
          viewBox="0 0 93.389 82.884"
        >
          <defs>
            <clipPath id="clip-path">
              <rect
                id="Rectangle_161406"
                data-name="Rectangle 161406"
                width="93.389"
                height="82.884"
                fill={color || '#242963'}
              />
            </clipPath>
          </defs>
          <g id="Group_151247" data-name="Group 151247" clipPath="url(#clip-path)">
            <path
              id="Path_84120"
              data-name="Path 84120"
              d="M58.788.318c-.448,1.061-1.287,3.1-1.287,3.1a2.54,2.54,0,0,0,.381.975c.51,8.973,1.9,17.607,2.434,26.523l-1.132.736a4.587,4.587,0,0,1-4.7-.114,1.79,1.79,0,0,0,.651-.736c.878-1.953,1.67-4.387.764-6.567-.425-1.132-1.443-2.689-2.831-2.774a3.192,3.192,0,0,0-1.981,1.1,5.808,5.808,0,0,0-1.22,2.959,10.612,10.612,0,0,0,1.069,5.038c-3.17,1.67-6.643,3.241-9.785,5-.841.424-1.415.962-2.18,1.358a15.248,15.248,0,0,0-.821,2.8c.**************.4.254,1.641-.339,3.4,0,5.1-.056,3.736.226,7.586.538,11.407.793-5.18,2.547-11.436,2.519-16.446,5.491-4.048,1.811-6.652,6.68-10.813,8.18a3.346,3.346,0,0,1-3.708-.764c-1.755-3.482.764-7.388,3.029-10.049a1.569,1.569,0,0,0,1.415.17c1.5-1.217,1.387-3.283,1.925-5.01-.056-.142-.2-.17-.339-.2-.2.255-.481.425-.453.821a1.9,1.9,0,0,1-1.613,1.019,1.882,1.882,0,0,1-.849-.708c-1.359-2.519-.538-5.916.34-8.407.623-2.576,2.548-4.642,3.086-7.3.113-.226-.17-.255-.312-.255A45.1,45.1,0,0,0,26.15,33.21c-.934,2.944-1.727,7.275.4,9.935a16.577,16.577,0,0,0-4.217,9.794,7.44,7.44,0,0,0,2.038,4.416c2.152,1.359,4.7.057,6.482-1.274C34.387,52.6,37.9,49.09,42.313,47.674A101.682,101.682,0,0,0,58.7,42.352c1.7-.764,3.482-1.3,5.1-2.208.425-.962.849-1.925,1.217-2.888-.255-.509-.595.028-.963-.056-6.255.764-12.709-.057-18.88-.453l-.113-.085c2.321-1.189,4.671-2.349,7.048-3.453l.764.849a5.292,5.292,0,0,0,5.237,1.217,6.2,6.2,0,0,0,3.04-2.29,8.552,8.552,0,0,0,.782-4.9c-.226-6.794-1.1-13.361-1.726-20.013.142-.226.17-.538.4-.679a8.636,8.636,0,0,1,1.529,1.7l.142-.114C61.675,5.922,59.965,2.947,59,0c-.113-.028-.187.23-.215.315m-7.695,25.4a1.135,1.135,0,0,1,.878-1.071,1.3,1.3,0,0,1,1.212.3,5.314,5.314,0,0,1,1.755,3.34,11.914,11.914,0,0,1-2.151,1.444,14.03,14.03,0,0,1-1.694-4.01"
              transform="translate(-16.008 0)"
              fill={color || '#242963'}
            />
            <path
              id="Path_84121"
              data-name="Path 84121"
              d="M59.03,106.507c.142-.821.906-1.755.028-2.491a1.1,1.1,0,0,0-.934-.4,2.489,2.489,0,0,0-1.161,1.3,21.648,21.648,0,0,0-1.132,4.671c-.311-.622-.425-1.358-.708-2.009-.453-1.217-1.217-3.028-2.774-3.114a4.739,4.739,0,0,0-.34,2.434,1.508,1.508,0,0,0,1.613.566c1.1,1.387,1.3,3.227,2.095,4.784l.226.227c.226-.368.142-.906.311-1.331.057-1.132.142-2.321.311-3.453.283-.736.283-1.67,1.076-2.151.311-.2.594.084.821.283.283.17.17.849.566.679"
              transform="translate(-37.255 -74.289)"
              fill={color || '#242963'}
            />
            <path
              id="Path_84122"
              data-name="Path 84122"
              d="M59.348,144.447c-.793-1.811.623-3.312,1.132-4.869-.085-.085-.142-.2-.255-.17a24.672,24.672,0,0,0-1.9,4.529l.085.113a2.525,2.525,0,0,0,.679,2.548.622.622,0,0,0,.962-.17,9.279,9.279,0,0,0,.906-2.519c-.453,0-.4.622-.679.906-.368.227-.736-.056-.934-.368"
              transform="translate(-41.818 -99.944)"
              fill={color || '#242963'}
            />
            <path
              id="Path_84123"
              data-name="Path 84123"
              d="M64.693,190.4c0,.538-.453,1.02-.142,1.5.17.085.226-.142.339-.2l6.822-2.888.2-.566c0-.425.425-.679.311-1.1l-.311-.028-7.048,2.916Z"
              transform="translate(-46.201 -134.152)"
              fill={color || '#242963'}
            />
            <path
              id="Path_84124"
              data-name="Path 84124"
              d="M71.909,65.647c-1.67.934-3.4,1.727-5.123,2.491-.368.227-.906.368-1.019.849a.37.37,0,0,0,.4.283c1.557-.481,3.142-.821,4.7-1.3,1.3-.283,4.246-3.991,4.416-4.671a.547.547,0,0,0-.368-.34c-2.661-.226-5.661-1.1-8.152.029l-1.529,2.6a.486.486,0,0,0,.4.34c1.811-.962,4.274-.595,6.284-.283"
              transform="translate(-46.765 -44.751)"
              fill={color || '#242963'}
            />
            <path
              id="Path_84125"
              data-name="Path 84125"
              d="M95.011,13.046c-.028-.085-.085-.226-.226-.226-3.68,2.236-14.832,8.577-16.672,9.6a4.407,4.407,0,0,0-.283,1.84s11.188-6.4,16.587-9.6Z"
              transform="translate(-55.798 -9.191)"
              fill={color || '#242963'}
            />
            <path
              id="Path_84126"
              data-name="Path 84126"
              d="M103.245,181.175a20.477,20.477,0,0,0,1.613-2.462l.028-.339-2.774-1.925-.283-.028c-.453.963-1.132,1.812-1.5,2.8.991.65,1.953,1.33,2.916,1.953"
              transform="translate(-71.93 -126.482)"
              fill={color || '#242963'}
            />
            <path
              id="Path_84127"
              data-name="Path 84127"
              d="M124.042,46.889l.028-.4a32.27,32.27,0,0,0-3.029-2.123,15.323,15.323,0,0,0-1.613,2.717c.934.764,2.038,1.444,2.972,2.293l.17-.085Z"
              transform="translate(-85.622 -31.81)"
              fill={color || '#242963'}
            />
            <path
              id="Path_84128"
              data-name="Path 84128"
              d="M149.445,104.205a20.174,20.174,0,0,0,1.189,3.227c.934,2.066,2.576,3.906,2.831,6.228a26.581,26.581,0,0,1-5.52,4.472c-4.3,3.029-9.058,5.038-13.644,7.331a48.558,48.558,0,0,1-7.728,3.255l-1.613.765c-.142.17-.283.509.028.594a67.436,67.436,0,0,1,9.709-1.7,67.368,67.368,0,0,0,17.012-10.105c2.208-1.585,2.491-4.02,3.085-6.284.028-2.8-1.67-4.9-2.774-7.247l.085-.142,1.415.623a.2.2,0,0,0,.34-.057,48.583,48.583,0,0,0-3.708-4.727l-.142-.028a12.034,12.034,0,0,0-.9,3.253.652.652,0,0,0,.339.54"
              transform="translate(-89.475 -71.989)"
              fill={color || '#242963'}
            />
            <path
              id="Path_84129"
              data-name="Path 84129"
              d="M134.371,163.95c-.085.991-.481,2.378.594,2.972a.86.86,0,0,0,.991-.339,4.48,4.48,0,0,0,.934-2.661l-.142-.028a9.164,9.164,0,0,0-.623,1.16.686.686,0,0,1-.849-.113c-.651-1.642.368-3.284.934-4.783,0-.085-.057-.227-.2-.17a18.577,18.577,0,0,0-1.641,3.963"
              transform="translate(-96.234 -114.691)"
              fill={color || '#242963'}
            />
            <path
              id="Path_84130"
              data-name="Path 84130"
              d="M210.514,182.445c-.085.991-.481,2.378.595,2.972a.859.859,0,0,0,.991-.339,4.482,4.482,0,0,0,.934-2.661l-.142-.028a9.149,9.149,0,0,0-.623,1.16.686.686,0,0,1-.849-.113c-.651-1.642.368-3.284.934-4.783,0-.085-.057-.226-.2-.17a18.6,18.6,0,0,0-1.642,3.963"
              transform="translate(-150.824 -127.951)"
              fill={color || '#242963'}
            />
            <path
              id="Path_84131"
              data-name="Path 84131"
              d="M286.6,182.445c-.085.991-.481,2.378.595,2.972a.859.859,0,0,0,.991-.339,4.482,4.482,0,0,0,.934-2.661l-.142-.028a9.149,9.149,0,0,0-.623,1.16.686.686,0,0,1-.849-.113c-.651-1.642.368-3.284.934-4.783,0-.085-.057-.226-.2-.17a18.6,18.6,0,0,0-1.642,3.963"
              transform="translate(-205.375 -127.951)"
              fill={color || '#242963'}
            />
            <path
              id="Path_84132"
              data-name="Path 84132"
              d="M262.4,145.241c-.075.871-.423,2.092.523,2.614a.757.757,0,0,0,.872-.3,3.944,3.944,0,0,0,.821-2.341l-.124-.025a8.029,8.029,0,0,0-.548,1.021.6.6,0,0,1-.747-.1c-.573-1.444.324-2.888.822-4.207,0-.075-.05-.2-.174-.15a16.344,16.344,0,0,0-1.444,3.486"
              transform="translate(-188.034 -101.622)"
              fill={color || '#242963'}
            />
            <path
              id="Path_84133"
              data-name="Path 84133"
              d="M202.229,125.575c-.075.872-.423,2.092.523,2.614a.757.757,0,0,0,.872-.3,3.943,3.943,0,0,0,.821-2.341l-.124-.025a8,8,0,0,0-.548,1.021.6.6,0,0,1-.747-.1c-.573-1.444.324-2.888.822-4.208,0-.074-.05-.2-.174-.149a16.338,16.338,0,0,0-1.444,3.486"
              transform="translate(-144.896 -87.522)"
              fill={color || '#242963'}
            />
            <path
              id="Path_84134"
              data-name="Path 84134"
              d="M104.369,82.211c-.075.872-.423,2.092.523,2.614a.756.756,0,0,0,.872-.3,3.942,3.942,0,0,0,.821-2.34l-.124-.025a7.991,7.991,0,0,0-.548,1.021.6.6,0,0,1-.747-.1c-.573-1.444.323-2.888.821-4.208,0-.075-.05-.2-.174-.149a16.345,16.345,0,0,0-1.444,3.486"
              transform="translate(-74.737 -56.433)"
              fill={color || '#242963'}
            />
            <path
              id="Path_84135"
              data-name="Path 84135"
              d="M169.119,152.622c-.075.871-.423,2.092.523,2.614a.757.757,0,0,0,.872-.3,3.943,3.943,0,0,0,.821-2.341l-.124-.025a8.028,8.028,0,0,0-.548,1.021.6.6,0,0,1-.747-.1c-.573-1.444.323-2.888.821-4.207,0-.075-.05-.2-.174-.15a16.341,16.341,0,0,0-1.444,3.486"
              transform="translate(-121.159 -106.913)"
              fill={color || '#242963'}
            />
            <path
              id="Path_84136"
              data-name="Path 84136"
              d="M174.767,6.224l.283,6.482,1.019,11.634c.368,6.227,1.387,12.4,1.076,18.71l.311.057a16.877,16.877,0,0,0,1.359-9.341l-1.783-21.258c.028-.906-.453-2.208.4-2.859a12.515,12.515,0,0,0,1.415,2.18c.142.057.425.085.425-.142A75.585,75.585,0,0,0,175.9,2.12h-.2c-.425,1.047-1.1,1.981-1.472,3.057Z"
              transform="translate(-124.911 -1.52)"
              fill={color || '#242963'}
            />
            <path
              id="Path_84137"
              data-name="Path 84137"
              d="M204.638,28.557a25.313,25.313,0,0,1,1.245-4.841c-.057-.113-.142-.254-.283-.17-.368.906-.566,1.925-.906,2.859-.254,1.557-1.528,3.821.17,5.1.226.142.679.17.793-.17l.623-2.349c.028-.142-.029-.283-.17-.283-.2.142-.2.368-.226.594-.057.2-.255.566-.566.4a1.5,1.5,0,0,1-.679-1.132"
              transform="translate(-146.252 -16.863)"
              fill={color || '#242963'}
            />
            <path
              id="Path_84138"
              data-name="Path 84138"
              d="M252.456,119.8a5.01,5.01,0,0,0-2.094-4.529c-3.284-1.981-6.963-2.972-9.879-5.8v-.142c.425,0,.906.056,1.359.085.113-.029.311.057.311-.142-1.529-1.019-3.227-1.727-4.755-2.718a12.868,12.868,0,0,0-.481,3.34l.736.339a18.26,18.26,0,0,0,7.643,5.888c2.038,1.076,4.444,1.67,5.8,3.821.021.47-.461.632-.934.991-3.821,2.038-6.457,4.331-10.335,6.227-2.406,1.1-4.033,2.7-6.92,1.967a2.1,2.1,0,0,1-1.047-1.16c-.34-2.8,1.755-4.869,2.633-7.331a.379.379,0,0,0-.4-.227,21.509,21.509,0,0,0-3.227,7.331c-.17,1.331-.453,3.34.991,4.275a4.731,4.731,0,0,0,3.68.425c3.425-1.161,5.788-2.9,8.958-4.628,2.519-1.5,4.108-2.8,6.542-4.529,1.3-.793,1.266-2.136,1.415-3.482"
              transform="translate(-165.417 -76.388)"
              fill={color || '#242963'}
            />
            <path
              id="Path_84139"
              data-name="Path 84139"
              d="M234.142,74.783l.34.028a27.676,27.676,0,0,0,.878-2.717c1.16-2.944.622-6.482.481-9.737l-1.019-10.5-.991-12.144.085-.17c.142.679.861,1.233,1.321,1.755.135.153.368.368.538.254-.368-2.859-2.029-5.237-2.878-7.9,0-.2-.255-.481-.453-.34l-1.3,2.208a2.05,2.05,0,0,0-.284.572s.563.787.426,1.324c.635,5.748,1.444,16.729,2.463,24.938.226,4.1,1.019,8.351.4,12.426"
              transform="translate(-165.51 -23.857)"
              fill={color || '#242963'}
            />
            <path
              id="Path_84140"
              data-name="Path 84140"
              d="M261.755,69.112a115.45,115.45,0,0,0-10.926,5.067,5.256,5.256,0,0,0-.368,1.755l.2.17.085-.085c3.368-2.208,7.162-3.538,10.785-5.265a13.288,13.288,0,0,0,.481-1.642Z"
              transform="translate(-179.565 -49.549)"
              fill={color || '#242963'}
            />
            <path
              id="Path_84141"
              data-name="Path 84141"
              d="M258.026,44.623c0-.085-.028-.255-.17-.17-.368,1.048-.764,2.095-1.075,3.171-.2,1.189-.793,2.972.509,3.736a.936.936,0,0,0,.821-.057c.142-.962.679-1.84.566-2.831-.425.142-.34.736-.566,1.075a.672.672,0,0,1-.651-.17c-.934-1.7.538-3.114.566-4.755"
              transform="translate(-183.874 -31.853)"
              fill={color || '#242963'}
            />
            <path
              id="Path_84142"
              data-name="Path 84142"
              d="M285.98,91.739a.2.2,0,0,0-.226-.142,26.564,26.564,0,0,0-1.047,3.708,3.258,3.258,0,0,0,.594,3.255,1.231,1.231,0,0,0,1.047-.113,24.628,24.628,0,0,0,.821-2.916l-.057-.17c-.453.2-.51.906-.764,1.359-.226.028-.566.142-.736-.085-1.019-1.613.142-3.312.368-4.9"
              transform="translate(-203.988 -65.666)"
              fill={color || '#242963'}
            />
            <path
              id="Path_84143"
              data-name="Path 84143"
              d="M199.993,65.074a6.983,6.983,0,0,0,.637-1.54c-.106-.452-.637-.771-.8-1.223.292-1.2.664-3.028-.452-3.878-1.434.08-2.284,1.541-2.656,2.736-.133,1.142,1.2,1.62,1.886,2.364a17.1,17.1,0,0,1-2.391,1.805c-1.461.983-3.027,1.78-4.382,2.843a.63.63,0,0,0,.478.133c2.364-1.143,4.993-1.992,6.773-4.3l.425.451c.212.187.239.532.478.611m-2.258-4.516a1.064,1.064,0,0,1,1.037-.425,2.449,2.449,0,0,1,.425,1.62,5.564,5.564,0,0,1-1.461-1.2"
              transform="translate(-137.536 -41.893)"
              fill={color || '#242963'}
            />
            <path
              id="Path_84144"
              data-name="Path 84144"
              d="M5.338,249.944c.185-.329.164-.782.453-1.029,1.4.823,3.435.411,4.978.164l.082.082c1.214.123,2.88.411,3.661-.72a4.815,4.815,0,0,0,1.769.473c.123-.309.371-.453.535-.72a2.4,2.4,0,0,0,2.119.391c.473-.267.494-.864.74-1.255.288.432.658,1.008,1.193,1.172a1.644,1.644,0,0,0,1.625-.165,2.432,2.432,0,0,0,.741-1.81c-.247-1.522-.6-3-.761-4.545.164-.453.35-.885.494-1.337a8.27,8.27,0,0,1-.7-.781c-.1-.124.021-.391-.185-.432-.206.535-.782,1.152-.308,1.728a43.646,43.646,0,0,0,.885,5.8c-.165.267-.473.247-.741.35-1.748.144-2.016-1.872-2.551-3H19.2L19,245.376a4.746,4.746,0,0,0,.452,1.214.557.557,0,0,1-.268.679,3,3,0,0,1-2.077-.1c.021-.308.123-.782-.082-.946a2.422,2.422,0,0,0-.535,1.481,8.317,8.317,0,0,1-1.522-.514c-.144-.226.185-.617-.1-.741-.268.308-.247.781-.494,1.131a5.359,5.359,0,0,1-2.592.473c-.185-.267.164-.514.082-.782h-.123a2.808,2.808,0,0,1-1.769.7,9.913,9.913,0,0,1-3.312-.062c-.844-.144-1.214-1.029-1.625-1.666h-.1a3.951,3.951,0,0,0-.329,1.193c.267.288.267.7.576.987.288.371.02.74-.267,1.029a3.785,3.785,0,0,1-3.867,1.193,1.674,1.674,0,0,1-.957-.679L0,250.026a3.128,3.128,0,0,0,2.767,1.852,3.724,3.724,0,0,0,2.571-1.934"
              transform="translate(0 -171.658)"
              fill={color || '#242963'}
            />
            <path
              id="Path_84145"
              data-name="Path 84145"
              d="M22.716,280.312l-.164-.041c-.906.391-1.872.658-2.818.967-.041.247-.35.617-.02.8.9-.391,1.913-.576,2.818-1.029a2.2,2.2,0,0,0,.185-.7"
              transform="translate(-14.025 -200.937)"
              fill={color || '#242963'}
            />
            <path
              id="Path_84146"
              data-name="Path 84146"
              d="M30.313,242.643a4.608,4.608,0,0,0-2.283-.309,1.358,1.358,0,0,0-.6,1.111c.164.1.267-.267.473-.309a2.322,2.322,0,0,1,1.4.082c-.288.226-.6.329-.864.576a.231.231,0,0,0,.041.329c.7-.288,1.872-.514,1.831-1.481"
              transform="translate(-19.668 -173.696)"
              fill={color || '#242963'}
            />
            <path
              id="Path_84147"
              data-name="Path 84147"
              d="M43.443,251.5l.926.617.165.021.617-.905.021-.226a3.709,3.709,0,0,0-1.214-.741,6.092,6.092,0,0,0-.617.987c-.576.288-.905-.473-1.42-.576a2.766,2.766,0,0,0-.679,1.152c.391.226.74.535,1.111.761.309-.39.555-1.007,1.09-1.09"
              transform="translate(-29.568 -179.421)"
              fill={color || '#242963'}
            />
            <path
              id="Path_84148"
              data-name="Path 84148"
              d="M44.811,244.084a3.56,3.56,0,0,0-.453.678c-.679.659.535.823.761,1.3h.1a6.868,6.868,0,0,0,.658-1.05c-.185-.432-.72-.6-1.069-.926"
              transform="translate(-31.656 -174.993)"
              fill={color || '#242963'}
            />
            <path
              id="Path_84149"
              data-name="Path 84149"
              d="M58,232.087c-.041.247-.227.494-.124.74l1.563-.679c.679-.411,1.81-.473,1.934-1.42a.16.16,0,0,0-.206-.1c-1.008.576-2.119.967-3.167,1.461"
              transform="translate(-41.473 -165.34)"
              fill={color || '#242963'}
            />
            <path
              id="Path_84150"
              data-name="Path 84150"
              d="M62.166,239.1a.851.851,0,0,0,.946-.35c.226-.1.452.021.679-.1.391-.185.35-.658.494-1.008-.021-.206-.061-.453-.247-.535-.185.123-.144.411-.226.617-.062.1.165.206-.021.267-.37.268-.412-.288-.679-.247l-.082.391a.583.583,0,0,1-.576.309c-.309-.164-.021-.473-.206-.658l-.082.061c-.123.371-.453.926,0,1.255"
              transform="translate(-44.403 -169.991)"
              fill={color || '#242963'}
            />
            <path
              id="Path_84151"
              data-name="Path 84151"
              d="M65.407,247.462l.638-1.111a5.026,5.026,0,0,0-.967-.741,2.931,2.931,0,0,0-.412.741c-.555.617.474.782.741,1.111"
              transform="translate(-46.246 -176.087)"
              fill={color || '#242963'}
            />
            <path
              id="Path_84152"
              data-name="Path 84152"
              d="M88.842,234.491c-.658.021-1.111.72-1.378,1.255l-.474-.123a1.2,1.2,0,0,0-.39.823c.041.1.185,0,.247-.082a2.274,2.274,0,0,0,1.687.02c.658-.205.8-.9.9-1.46-.061-.267-.37-.391-.6-.432m.1.9c-.062.226-.391.267-.576.371l-.493.021a1.174,1.174,0,0,1,.576-.515.452.452,0,0,1,.494.123"
              transform="translate(-62.087 -168.115)"
              fill={color || '#242963'}
            />
            <path
              id="Path_84153"
              data-name="Path 84153"
              d="M94.193,259.946a3.057,3.057,0,0,0-1.316-2.88,3.879,3.879,0,0,0-.864.967,3.662,3.662,0,0,0-.164,2.2,1.031,1.031,0,0,0,.9.493c.473-.041.7-.473.864-.864.391.8-.453,1.42-.823,2.078a3.956,3.956,0,0,1-3.6,1.707c-.473-.123-.87-.4-1.343-.506-.1.062-.139.075-.139.2a4.855,4.855,0,0,0,3.536,1.213c.144-.1,2.456-.544,2.944-4.607m-1.769-.679c-.226-.061-.164-.309-.061-.432.144-.1.185-.309.35-.35.247.164.473.473.309.761a.492.492,0,0,1-.6.02"
              transform="translate(-62.885 -184.3)"
              fill={color || '#242963'}
            />
            <path
              id="Path_84154"
              data-name="Path 84154"
              d="M208.885,259.946a3.057,3.057,0,0,0-1.317-2.88,3.883,3.883,0,0,0-.864.967,3.66,3.66,0,0,0-.165,2.2,1.031,1.031,0,0,0,.9.493c.473-.041.7-.473.864-.864.391.8-.453,1.42-.823,2.078a3.955,3.955,0,0,1-3.6,1.707c-.473-.123-.87-.4-1.343-.506-.1.062-.138.075-.138.2a4.855,4.855,0,0,0,3.536,1.213c.144-.1,2.456-.544,2.944-4.607m-1.769-.679c-.226-.061-.165-.309-.062-.432.144-.1.185-.309.35-.35.247.164.473.473.308.761a.492.492,0,0,1-.6.02"
              transform="translate(-145.112 -184.3)"
              fill={color || '#242963'}
            />
            <path
              id="Path_84155"
              data-name="Path 84155"
              d="M88.847,244.3a3.712,3.712,0,0,0-.37,1.317c.288,1.769.72,3.476.864,5.306.062.33-.13.638.076.864a4.849,4.849,0,0,0,.418-1.645,49.918,49.918,0,0,0-.823-5.656Z"
              transform="translate(-63.432 -175.148)"
              fill={color || '#242963'}
            />
            <path
              id="Path_84156"
              data-name="Path 84156"
              d="M206.091,232.43c-.658.021-1.111.72-1.378,1.255l-.473-.123a1.2,1.2,0,0,0-.391.823c.041.1.185,0,.247-.082a2.277,2.277,0,0,0,1.687.02c.658-.206.8-.9.9-1.46-.061-.267-.37-.391-.6-.432m.1.905c-.061.226-.391.267-.576.37l-.493.021a1.172,1.172,0,0,1,.576-.515.451.451,0,0,1,.493.124"
              transform="translate(-146.147 -166.638)"
              fill={color || '#242963'}
            />
            <path
              id="Path_84157"
              data-name="Path 84157"
              d="M206.1,242.24a3.705,3.705,0,0,0-.37,1.316c.288,1.769.72,3.477.864,5.307.062.329-.13.638.076.864a4.851,4.851,0,0,0,.418-1.646,49.96,49.96,0,0,0-.823-5.657Z"
              transform="translate(-147.493 -173.671)"
              fill={color || '#242963'}
            />
            <path
              id="Path_84158"
              data-name="Path 84158"
              d="M103.121,243.714l-.164-.02-4.2,2.1c0,.267-.309.6-.021.782a42.02,42.02,0,0,1,4.176-2.139,1.527,1.527,0,0,0,.206-.72"
              transform="translate(-70.702 -174.713)"
              fill={color || '#242963'}
            />
            <path
              id="Path_84159"
              data-name="Path 84159"
              d="M118.536,256.3a5.561,5.561,0,0,1,2.839-3.97,4.225,4.225,0,0,0,1.214.782,2.568,2.568,0,0,0,1.872-.206,2.451,2.451,0,0,0,.452-1.666,2.7,2.7,0,0,0-.925-2.1h-.062a3.585,3.585,0,0,0-.247,1.255,2.876,2.876,0,0,1,.782,1.276,1.981,1.981,0,0,1-1.3.39,2.178,2.178,0,0,1-1.173-.432c.041-.411.453-.741.226-1.193-.062-.309-.391-.474-.617-.6a2.17,2.17,0,0,0-2.139.391c-.391.329-.206.946-.35,1.378.329.165.885,0,1.173.267a5.976,5.976,0,0,0-1.481,2.16c-.679,1.625-1.049,3.991-.061,5.6,1.316,1.872,3.723,2.1,5.821,1.707a22,22,0,0,0,4.093-1.44c.1-.062.02-.164-.02-.226-1.379.267-2.777.288-4.2.473a8.328,8.328,0,0,1-4.052-.555,3.521,3.521,0,0,1-1.851-3.291"
              transform="translate(-84.697 -178.62)"
              fill={color || '#242963'}
            />
            <path
              id="Path_84160"
              data-name="Path 84160"
              d="M125.559,272.225l.741-.371c.6-.452,1.831-.535,1.707-1.543l-.144-.02a14.458,14.458,0,0,1-1.913,1.049c-.411.042-.411.6-.391.885"
              transform="translate(-90.013 -193.781)"
              fill={color || '#242963'}
            />
            <path
              id="Path_84161"
              data-name="Path 84161"
              d="M136.824,236.676a7.063,7.063,0,0,0-2.16-.329c-.432.1-.535.576-.658.9.041.165.206.042.247-.041a1.7,1.7,0,0,1,1.543.1c-.288.267-.678.391-.864.72.021.082.144.062.247.062a8.378,8.378,0,0,0,1.5-.864c.021-.185.329-.371.144-.555"
              transform="translate(-96.074 -169.423)"
              fill={color || '#242963'}
            />
            <path
              id="Path_84162"
              data-name="Path 84162"
              d="M144.771,257.887h-.144a4.114,4.114,0,0,0-.247,1.131,9.742,9.742,0,0,1,.926,1.851,5.634,5.634,0,0,1-3.559,2.284,9.2,9.2,0,0,1-2.093-.54.333.333,0,0,1,0,.051,1.432,1.432,0,0,0,.636.756c.969.45,2.3.967,3.353.165a4.445,4.445,0,0,0,2.221-3.23c.02-.987-.658-1.666-1.09-2.468"
              transform="translate(-100.121 -184.889)"
              fill={color || '#242963'}
            />
            <path
              id="Path_84163"
              data-name="Path 84163"
              d="M138.8,265.626a5.994,5.994,0,0,0,1.09.782,3.716,3.716,0,0,1,.555-.844c.473-.514,1.007.458,1.48.5a5.013,5.013,0,0,0,.576-1.152l-1.048-.726h-.144c-.37.309-.247.967-.8,1.049a12.584,12.584,0,0,0-1.172-.761,3.468,3.468,0,0,0-.535,1.152"
              transform="translate(-99.513 -189.405)"
              fill={color || '#242963'}
            />
            <path
              id="Path_84164"
              data-name="Path 84164"
              d="M154.061,245.047a1.46,1.46,0,0,0-.946-.7,2.881,2.881,0,0,0-.432,1.172c.35.144.6.555.926.658Z"
              transform="translate(-109.464 -175.182)"
              fill={color || '#242963'}
            />
            <path
              id="Path_84165"
              data-name="Path 84165"
              d="M169.514,237.817c-.072-.16.021-.311-.132-.445-.205.013-.266.347-.356.559,0,.37-.4.83.032,1.132.311,1.938.845,3.821,1.159,5.8l-.134.153c-1.063.46-2.133-.151-3.119-.417a3.32,3.32,0,0,1-1.194.984,2.246,2.246,0,0,1-1.074.276,5.441,5.441,0,0,0-.724-1.623c-.2-.255-.337-.741-.746-.694a2.471,2.471,0,0,0-1.2,1.768c0,.577-.314,1.525.426,1.807.473.3,1.078.1,1.564.27-1.18,1.519-2.885,2.887-4.883,2.274a2.163,2.163,0,0,0-.652-.2,1.773,1.773,0,0,0,1.437.851,4.7,4.7,0,0,0,2.488-.011,6.559,6.559,0,0,0,2.178-3.053,5.538,5.538,0,0,0,1.486-.612c.294-.245.494-.65.834-.816a3.989,3.989,0,0,0,2.918.491.908.908,0,0,0,.588-.471c.74-1.944-.3-4-.551-5.963l.3-1.4ZM162.5,245.63a1.269,1.269,0,0,1,.695-.725l.361.472-.127.585a1.454,1.454,0,0,1-.928-.332"
              transform="translate(-113.617 -170.181)"
              fill={color || '#242963'}
            />
            <path
              id="Path_84166"
              data-name="Path 84166"
              d="M174.233,234.414l3.909-2.016.185-.391c.082-.1.082-.329-.1-.309-1.275.72-2.613,1.317-3.95,1.975-.041.247-.268.555-.041.741"
              transform="translate(-124.843 -166.112)"
              fill={color || '#242963'}
            />
            <path
              id="Path_84167"
              data-name="Path 84167"
              d="M178.944,249.436c.206.02.309.309.494.309.309-.411.473-.987.967-1.172.391.206.72.555,1.111.74l.6-.967v-.226l-.967-.72H181c-.391.329-.329,1.09-.988,1.07l-.987-.555a3.682,3.682,0,0,0-.617,1.151Z"
              transform="translate(-127.908 -177.37)"
              fill={color || '#242963'}
            />
            <path
              id="Path_84168"
              data-name="Path 84168"
              d="M180.439,239.069c-.412.1-.371.6-.432.946a.5.5,0,0,0,.329.37c.37-.041.761-.062.987-.452.288.206.555-.041.8-.165a2.713,2.713,0,0,0,.267-.9c-.061-.144-.061-.453-.288-.33a1.729,1.729,0,0,0-.185.761c-.411.309-.288-.309-.6-.309a.577.577,0,0,0-.123.309c0,.288-.329.37-.555.391-.473.041-.082-.432-.206-.617"
              transform="translate(-129.054 -170.993)"
              fill={color || '#242963'}
            />
            <path
              id="Path_84169"
              data-name="Path 84169"
              d="M302.607,247.968c-.412.1-.371.6-.432.946a.5.5,0,0,0,.33.37c.37-.041.761-.062.987-.452.288.206.555-.041.8-.165a2.712,2.712,0,0,0,.267-.9c-.061-.144-.061-.453-.288-.33a1.729,1.729,0,0,0-.185.761c-.411.309-.288-.309-.6-.309a.577.577,0,0,0-.123.309c0,.288-.329.37-.555.391-.473.041-.082-.432-.206-.617"
              transform="translate(-216.64 -177.373)"
              fill={color || '#242963'}
            />
            <path
              id="Path_84170"
              data-name="Path 84170"
              d="M219.147,244.8a.165.165,0,0,0-.185-.1,19.318,19.318,0,0,1-2.8,1.5c-.082.268-.288.555-.123.823.576-.329,1.214-.637,1.831-.967.494-.309,1.214-.576,1.275-1.255"
              transform="translate(-154.843 -175.433)"
              fill={color || '#242963'}
            />
            <path
              id="Path_84171"
              data-name="Path 84171"
              d="M233.315,275.154c.1-.226.37-.473.226-.72l-3.147,1.316c-.062.227-.288.474-.144.741Z"
              transform="translate(-165.04 -196.752)"
              fill={color || '#242963'}
            />
            <path
              id="Path_84172"
              data-name="Path 84172"
              d="M235.6,257.89a16.569,16.569,0,0,1-1.172-3.209c-.041-.1-.123-.288-.247-.247a4.144,4.144,0,0,0,0,1.234c-.185.309-.617.35-.946.453a4.987,4.987,0,0,0-1.481,2.2c.782.329,1.852.761,2.571.021.185-.206.227-.535.494-.617.535.72.535,1.687,1.358,2.242,1.049.638,2.365.245,3.415-.1a3.876,3.876,0,0,0,2.242-2.345c-.02-.062-.041-.144-.144-.124a7.039,7.039,0,0,1-2.324,1.234c-.576-.082-.926-.74-1.111-1.234.309-.576,1.008-.637,1.5-.987.473-.185.7.329,1.09.411.1-.062.247-.144.185-.288-.452-.473-1.008-1.09-1.769-.9-1.028.556-1.337,1.79-1.707,2.818a1.084,1.084,0,0,1,.206.514,2.358,2.358,0,0,1-2.16-1.07m-2.8-.638c.411-.473,1.255-.329,1.5-1.049a2.807,2.807,0,0,1,.308,1.019c0,.039-.1.09-.123.132a2.019,2.019,0,0,1-1.687-.1"
              transform="translate(-166.149 -182.409)"
              fill={color || '#242963'}
            />
            <path
              id="Path_84173"
              data-name="Path 84173"
              d="M233.912,245.531a8.635,8.635,0,0,0-1.214-.864,3.864,3.864,0,0,0-.658,1.111l1.008.8.144-.082a1.707,1.707,0,0,1,1.09-.864l.74.494c.493.391.6-.473.946-.658l.061-.247a6.254,6.254,0,0,0-1.131-.8c-.473.309-.473.926-.987,1.111"
              transform="translate(-166.358 -175.234)"
              fill={color || '#242963'}
            />
            <path
              id="Path_84174"
              data-name="Path 84174"
              d="M251.7,248.438l3.147-1.666.267-.6a.155.155,0,0,0-.144-.082c-1.049.493-2.016,1.111-3.086,1.543-.082.247-.391.576-.185.8"
              transform="translate(-180.406 -176.433)"
              fill={color || '#242963'}
            />
            <path
              id="Path_84175"
              data-name="Path 84175"
              d="M275.128,243.055c.247,1.892.637,3.846.72,5.78-.392.576-1.029.288-1.5.082-.329-.082-.576-.452-.926-.412a2.676,2.676,0,0,1-1.728.967c-.555-.082-1.276-.144-1.46-.823-.411-2.16-.7-4.361-1.091-6.521h-.123a3.924,3.924,0,0,0-.206,1.316c.329,2.14.617,4.3.987,6.418a1.554,1.554,0,0,0,1.378.885,2.4,2.4,0,0,0,2.057-1.029,5.548,5.548,0,0,0,2.469.864,23.356,23.356,0,0,1,2.941-.061,5.919,5.919,0,0,0,4.279-1.748c.165-.206.083-.535.288-.72a4.186,4.186,0,0,0,.535,1.645,1.642,1.642,0,0,0,1.44.37.664.664,0,0,0,.391-.453c.9-1.913-.308-3.867-.37-5.821l.247-1.173c-.185-.206-.493-.391-.638-.658-.185-.144-.082-.453-.288-.535-.205.514-.555,1.214-.164,1.769.391,1.769.7,3.661,1.028,5.472a1.077,1.077,0,0,1-1.542-.741c-.412-1.954-.885-3.887-1.111-5.924-.061-.165.02-.453-.185-.535a3.4,3.4,0,0,0-.309,1.213c.185,1.687.6,3.312.885,4.958a4.967,4.967,0,0,1-2.839,1.44c.144-.576.638-1.028.515-1.646a1.752,1.752,0,0,0-1.008-.741c-1.481-.452-2.345,1.09-3.477,1.769l-.144.02a3.022,3.022,0,0,1,.144-.617c-.123-1.46-.349-2.9-.472-4.32.04-.061.082.02.123.02a5.36,5.36,0,0,0,.329-1.255l-.74-.617v-.185c-.061-.1-.082-.288-.247-.288-.206.555-.576,1.254-.185,1.831m3.168,4.855a2.114,2.114,0,0,1,1.646.453,9.881,9.881,0,0,1-3.765.967,7.362,7.362,0,0,1,2.119-1.42"
              transform="translate(-192.722 -172.942)"
              fill={color || '#242963'}
            />
            <path
              id="Path_84176"
              data-name="Path 84176"
              d="M277.117,254.192c.844-.412,1.666-.844,2.489-1.317,0-.247.473-.514.123-.679l-2.489,1.3c-.082.206-.391.535-.123.7"
              transform="translate(-198.599 -180.809)"
              fill={color || '#242963'}
            />
            <path
              id="Path_84177"
              data-name="Path 84177"
              d="M282.792,278.431a2.93,2.93,0,0,0-.535,1.213,4.5,4.5,0,0,0,.988.6,8.772,8.772,0,0,0,.6-1.152,6.887,6.887,0,0,0-1.049-.658"
              transform="translate(-202.36 -199.617)"
              fill={color || '#242963'}
            />
            <path
              id="Path_84178"
              data-name="Path 84178"
              d="M300.941,276.177a11.445,11.445,0,0,0-1.151.494c-.6.329-1.275.494-1.831.843-.041.185-.35.514-.02.617l2.8-1.316c.042-.206.412-.391.206-.638"
              transform="translate(-213.495 -198.001)"
              fill={color || '#242963'}
            />
            <path
              id="Path_84179"
              data-name="Path 84179"
              d="M318.176,276.067c.988-.412,1.976-.8,2.962-1.193.1-.205.391-.452.247-.658-1.028.371-2.057.8-3.086,1.172-.082.206-.391.514-.124.679"
              transform="translate(-228.036 -196.595)"
              fill={color || '#242963'}
            />
          </g>
        </svg>
      </Box>
    );

    if (disabledLink) {
      return logo;
    }

    return (
      <Link component={RouterLink} href="/" sx={{ display: 'contents' }}>
        {logo}
      </Link>
    );
  }
);

export default Logo;
