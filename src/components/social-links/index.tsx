import { Box, Typography, IconButton } from '@mui/material';
import { Icon } from '@iconify/react';
import { useTheme } from '@mui/material/styles';

const socialLinks = [
  { icon: 'mdi:twitter', url: '#' },
  { icon: 'mdi:instagram', url: '#' },
  { icon: 'ri:facebook-fill', url: '#' },
];

const SocialLinks = () => {
  const theme = useTheme();
  return (
    <Box>
      <Typography sx={{ mb: 2 }}>:تواصل معنا على</Typography>

      <Box sx={{ display: 'flex', gap: 2 }}>
        {socialLinks.map((link, index) => (
          <IconButton
            key={index}
            onClick={() => window.open(link.url, '_blank')}
            sx={{
              border: 1,
              borderColor: theme.palette.secondary.main,
              bgcolor: theme.palette.secondary.main,
              '&:hover': {
                borderColor: theme.palette.primary.main,
                bgcolor: theme.palette.primary.main,
              },
            }}
          >
            <Icon icon={link.icon} width={24} height={24} color={theme.palette.common.white} />
          </IconButton>
        ))}
      </Box>
    </Box>
  );
};

export default SocialLinks;
