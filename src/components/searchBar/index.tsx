import {
  Box,
  TextField,
  Paper,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  Avatar,
  Typography,
  Skeleton,
  InputAdornment,
} from '@mui/material';

import { debounce } from 'lodash';
import { useEffect, useMemo, useRef, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useLocales } from 'src/locales';
import { paths } from 'src/routes/paths';
import { extractAllProducts } from 'src/utils/flatten-array';
import { useGetHome } from 'src/api/home';
import { ICONS_CATEGORIES } from 'src/utils/constant';
import Iconify from '../iconify';

interface Props {
  open: boolean;
  onClose: () => void;
}

export default function SearchOverlay({ open, onClose }: Props) {
  const router = useRouter();
  const { t } = useLocales();
  const [query, setQuery] = useState('');
  const [results, setResults] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const boxRef = useRef<HTMLDivElement>(null);
  const { homeData } = useGetHome();
  const products = useMemo(
    () => [...extractAllProducts(homeData.products_by_category), ...homeData.new_products],
    [homeData.products_by_category, homeData.new_products]
  );

  const handleNavigateProduct = (id: string) => {
    router.push(paths.product.details(`${id}`));
    onClose();
  };

  const handleNavigateCategory = (id: string) => {
    router.push(`/category/${id}`);
    onClose();
  };

  const debouncedSearch = useMemo(
    () =>
      debounce((search: string) => {
        setLoading(true);

        if (!search.trim()) {
          setResults([]);
          setLoading(false);
          return;
        }

        // هنا نجري الفلترة
        const filtered = products.filter((product: any) =>
          product.name.toLowerCase().includes(search.toLowerCase())
        );

        // تأخير تعيين النتائج وإيقاف التحميل لتظهر Skeletons بوضوح
        setTimeout(() => {
          setResults(filtered);
          setLoading(false);
        }, 300); // يمكنك ضبط المدة حسب الحاجة
      }, 10),
    [products]
  );
  useEffect(() => {
    debouncedSearch(query);
    return () => debouncedSearch.cancel();
  }, [query, debouncedSearch]);

  useEffect(() => {
    const handleEsc = (e: KeyboardEvent) => {
      if (e.key === 'Escape') onClose();
    };
    if (open) {
      document.addEventListener('keydown', handleEsc);
    }
    return () => document.removeEventListener('keydown', handleEsc);
  }, [open, onClose]);

  if (!open) return null;

  return (
    <Box
      ref={boxRef}
      sx={{
        position: 'fixed',
        top: 0,
        left: 0,
        width: '100vw',
        height: '100vh',
        bgcolor: 'rgba(0, 0, 0, 0.3)',
        backdropFilter: 'blur(2px)',
        WebkitBackdropFilter: 'blur(2px)',
        zIndex: 1300,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
      }}
      onClick={(e) => {
        if (boxRef.current && e.target === boxRef.current) {
          onClose();
        }
      }}
    >
      <Box
        sx={{
          width: '100%',
          maxWidth: 600,
          bgcolor: 'background.paper',
          borderRadius: 2,
          boxShadow: 24,
          p: 3,
        }}
      >
        <TextField
          autoFocus
          fullWidth
          placeholder={t('search')}
          variant="outlined"
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <Iconify icon="tabler:search" />
              </InputAdornment>
            ),
          }}
          sx={{
            mb: 2,
            '& .MuiOutlinedInput-root': {
              borderRadius: 2,
              bgcolor: 'white',
              px: 2,
            },
          }}
        />

        <Paper elevation={0} sx={{ maxHeight: 400, overflowY: 'auto' }}>
          {loading && (
            <List>
              {[...Array(5)].map((_, index) => (
                <ListItem key={index}>
                  <ListItemAvatar>
                    <Skeleton variant="rounded" width={40} height={40} />
                  </ListItemAvatar>
                  <ListItemText
                    primary={<Skeleton width="80%" />}
                    secondary={<Skeleton width="40%" />}
                  />
                </ListItem>
              ))}
            </List>
          )}

          {!loading && query && results.length === 0 && (
            <Box sx={{ p: 2, textAlign: 'center', color: 'text.secondary' }}>
              <Typography>{t('search_not_found')}</Typography>
            </Box>
          )}

          {results.length > 0 && (
            <List>
              {results.map((product) => (
                <ListItem
                  button
                  key={product.id}
                  onClick={() => handleNavigateProduct(String(product.id))}
                >
                  <ListItemAvatar>
                    <Avatar
                      variant="rounded"
                      src={product.image}
                      alt={product.name}
                      sx={{ width: 40, height: 40 }}
                    />
                  </ListItemAvatar>
                  <ListItemText primary={product.name} />
                </ListItem>
              ))}
            </List>
          )}

          {!loading && !query && homeData.categories.length > 0 && (
            <List>
              {homeData.categories.map((cat, index) => (
                <ListItem
                  button
                  key={cat.id}
                  onClick={() => handleNavigateCategory(String(cat.id))}
                >
                  <ListItemAvatar>
                    <Avatar
                      variant="rounded"
                      src={`/assets/icons/home/<USER>/${ICONS_CATEGORIES[index].icon}.svg`}
                      alt={cat.name}
                      sx={{ width: 40, height: 40 }}
                    />
                  </ListItemAvatar>
                  <ListItemText primary={cat.name} />
                </ListItem>
              ))}
            </List>
          )}
        </Paper>
      </Box>
    </Box>
  );
}
