import { useState, useEffect } from 'react';
import { IReview } from 'src/types/product';

interface RatingCount {
  rating: number;
  count: number;
}

export const useReviews = (initialReviews: IReview[]) => {
  const [reviews, setReviews] = useState<IReview[]>(initialReviews || []);
  const [ratingCounts, setRatingCounts] = useState<RatingCount[]>([
    { rating: 5, count: 10 },
    { rating: 4, count: 3 },
    { rating: 3, count: 20 },
    { rating: 2, count: 12 },
    { rating: 1, count: 50 },
  ]);

  useEffect(() => {
    if (!Array.isArray(initialReviews)) return;
    setReviews(initialReviews);
  }, [initialReviews]);

  useEffect(() => {
    if (!Array.isArray(reviews)) return;

    const counts = [5, 4, 3, 2, 1].map((rating) => ({
      rating,
      count: reviews.filter((review) => review?.rating === rating).length,
    }));
    setRatingCounts(counts);
  }, [reviews]);

  const handleSubmitReview = (rating: number, comment: string) => {
    const newReview: IReview = {
      id: Date.now(),
      rating,
      comment,
      image: '',
      name: 'اسم المستخدم',
      added_at: new Date().toLocaleDateString(),
    };

    setReviews([newReview, ...reviews]);
  };

  return {
    reviews,
    ratingCounts,
    handleSubmitReview,
  };
};
