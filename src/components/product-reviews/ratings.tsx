import { Box, Typography, Skeleton } from '@mui/material';
import { useGetProductReviews } from 'src/api/product';
import { useLocales } from 'src/locales';
import ReviewSummary from './review-summary';
import ReviewList from './review-list';
import ReviewForm from './review-form';
import { useReviews } from './useReviews';

const Ratings = ({ bookID }: { bookID: string }) => {
  const { t } = useLocales();
  const { reviews: initReviews, reviewsLoading } = useGetProductReviews(bookID);
  const { reviews, ratingCounts, handleSubmitReview } = useReviews(initReviews);

  if (reviewsLoading) {
    return (
      <Box
        sx={{
          padding: '45px',
          bgcolor: 'background.paper',
          boxShadow: (theme) => theme.shadows[1],
          borderRadius: '30px',
        }}
      >
        <Skeleton width={200} height={32} sx={{ mb: 3 }} />
        <Box sx={{ display: 'flex', gap: 4, flexDirection: { xs: 'column', md: 'row' } }}>
          <Box sx={{ flex: 1 }}>
            {[...Array(3)].map((_, index) => (
              <Box key={index} sx={{ mb: 2 }}>
                <Skeleton variant="rectangular" height={100} />
              </Box>
            ))}
          </Box>
          <Box
            sx={{
              width: '361px',
              height: '264px',
              maxHeight: '264px',
              maxWidth: '361px',
            }}
          >
            <Skeleton variant="rectangular" height="100%" />
          </Box>
        </Box>
      </Box>
    );
  }

  return (
    <Box
      sx={{
        padding: '45px',
        bgcolor: 'background.paper',
        boxShadow: (theme) => theme.shadows[1],
        borderRadius: '30px',
      }}
    >
      <Typography variant="h5" sx={{ mb: 3 }}>
        {t('product_details.reviews')}
      </Typography>

      <Box sx={{ display: 'flex', gap: 4, flexDirection: { xs: 'column', md: 'row' } }}>
        <Box sx={{ flex: 1 }}>
          <ReviewList reviews={reviews || []} />
          <ReviewForm onSubmit={handleSubmitReview} />
        </Box>
        <Box
          sx={{
            width: '100%',
            height: '264px',
            maxHeight: '264px',
            maxWidth: '361px',
          }}
        >
          <ReviewSummary ratingCounts={ratingCounts} />
        </Box>
      </Box>
    </Box>
  );
};

export default Ratings;
