import { Box, Divider } from '@mui/material';
import { IReview } from 'src/types/product';
import ReviewItem from './review-item';

const ReviewList = ({ reviews }: { reviews: IReview[] }) => (
  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
    {reviews.map((review) => (
      <Box key={review.id}>
        <ReviewItem
          rating={review.rating}
          comment={review.comment}
          username={review.name}
          date={review.added_at}
        />
        <Divider sx={{ flexGrow: 1, borderColor: '#E8E8E8', alignSelf: 'center' }} />
      </Box>
    ))}
  </Box>
);

export default ReviewList;
