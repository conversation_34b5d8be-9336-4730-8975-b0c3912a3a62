import { Icon } from '@iconify/react';
import { Box } from '@mui/material';
import React, { useState } from 'react';

const StarRating = ({
  width,
  height,
  rating = 0,
  disabled = false,
}: {
  width: string;
  height: string;
  rating?: number;
  disabled?: boolean;
}) => {
  const [hoveredIndex, setHoveredIndex] = useState(-1);
  // Render 5 stars using map
  return (
    <Box sx={{ display: 'flex', gap: '2px' }}>
      {Array.from({ length: 5 }).map((_, index) => (
        <Icon
          key={index}
          icon="mdi:star"
          style={{
            color:
              hoveredIndex >= index || (disabled && rating > index)
                ? '#FFB300' // Gold color when hovered or set rating
                : '#ccc', // Default grey
            cursor: disabled ? 'default' : 'pointer',
            transition: 'color 0.3s ease',
          }}
          width={width}
          height={height}
          // Conditionally disable mouse events if "disabled" is true
          onMouseEnter={() => !disabled && setHoveredIndex(index)}
          onMouseLeave={() => !disabled && setHoveredIndex(-1)}
        />
      ))}
    </Box>
  );
};

export default StarRating;
