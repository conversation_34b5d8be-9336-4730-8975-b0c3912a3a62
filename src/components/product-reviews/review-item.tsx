import { Box, Tooltip, Typography } from '@mui/material';
import StarRating from './five-star-rating';

interface ReviewItemProps {
  rating: number;
  comment: string;
  username: string;
  date: string;
}

const ReviewItem = ({ rating, comment, username, date }: ReviewItemProps) => (
  <Box sx={{ mb: 3 }}>
    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 1 }}>
      <Tooltip title={`${(rating * 20).toFixed(2)}%`} placement="right" followCursor>
        <Box>
          <StarRating rating={rating} disabled width="20px" height="20px" />
        </Box>
      </Tooltip>
    </Box>

    <Typography sx={{ mb: 1 }}>{comment}</Typography>
    <Box sx={{ display: 'flex', gap: '15px' }}>
      <Typography color="primary">{username}</Typography>
      <Typography color="primary" sx={{ direction: 'ltr' }}>
        {date}
      </Typography>
    </Box>
  </Box>
);

export default ReviewItem;
