import { useState } from 'react';
import { Box, Typography, TextField } from '@mui/material';
import CustomButton from 'src/layouts/_common/button-detail';
import { useLocales } from 'src/locales';
import StarRating from './five-star-rating';

interface ReviewFormProps {
  onSubmit: (rating: number, comment: string) => void;
}

const ReviewForm = ({ onSubmit }: ReviewFormProps) => {
  const { t } = useLocales();
  const [rating, setRating] = useState<number | null>(0);
  const [comment, setComment] = useState('');

  const handleSubmit = () => {
    if (rating && comment) {
      onSubmit(rating, comment);
      setRating(0);
      setComment('');
    }
  };

  return (
    <Box sx={{ mt: 4 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', width: '100%' }}>
        <Typography variant="h6" sx={{ mb: 2, margin: '0px' }}>
          {t('product_details.add_review')}
        </Typography>
        <Box sx={{ display: 'flex' }}>
          <Typography sx={{ mb: 2, margin: '0px' }}>{t('product_details.add_rating')}</Typography>
          <StarRating width="17px" height="17px" />
        </Box>
      </Box>

      <TextField
        fullWidth
        multiline
        rows={4}
        value={comment}
        onChange={(e) => setComment(e.target.value)}
        placeholder={t('product_details.add_review_placeholder')}
        sx={{ mb: 2 }}
      />
      <CustomButton
        disabled={!rating || !comment}
        variant="contained"
        onClick={handleSubmit}
        color="secondary"
      >
        {t('product_details.add_review_button')}
      </CustomButton>
    </Box>
  );
};

export default ReviewForm;
