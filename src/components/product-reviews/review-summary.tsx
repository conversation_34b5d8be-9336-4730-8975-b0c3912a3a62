import { Box, Typography } from '@mui/material';
import { useLocales } from 'src/locales';
import StarRating from './five-star-rating';

interface RatingCount {
  rating: number;
  count: number;
}

interface ReviewSummaryProps {
  ratingCounts: RatingCount[];
}

const ReviewSummary = ({ ratingCounts }: ReviewSummaryProps) => {
  const { t } = useLocales();
  return (
    <Box
      width="100%"
      height="100%"
      sx={{ p: 3, border: '1px solid', borderColor: 'divider', borderRadius: '5px' }}
    >
      <Typography variant="h6" sx={{ mb: 2 }}>
        {t('product_details.all_ratings')}
      </Typography>

      {ratingCounts.map(({ rating, count }) => (
        <Box
          key={rating}
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            mb: 1,
          }}
        >
          <StarRating rating={rating} disabled width="20px" height="20px" />
          <Box
            sx={{
              flex: 1,
              mx: 2,
              height: 8,
              bgcolor: 'grey.100',
              borderRadius: 1,
              overflow: 'hidden',
              '@media (max-width:500px)': {
                display: 'none',
              },
            }}
          >
            <Box
              sx={{
                width: `${(count / 5) * 100}%`,
                height: '100%',
                bgcolor: 'primary.main',
                '@media (max-width:500px)': {
                  display: 'none',
                },
              }}
            />
          </Box>
          <Typography sx={{ minWidth: '2rem', textAlign: 'right' }}>{count}</Typography>
        </Box>
      ))}
    </Box>
  );
};

export default ReviewSummary;
