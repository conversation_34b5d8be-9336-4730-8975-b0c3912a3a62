import { Box, Radio } from '@mui/material';
import { Icon } from '@iconify/react';
import Image from 'next/image';

interface PaymentCardProps {
  value: string;
  selected: boolean;
  imageSrc: string;
  imageAlt: string;
  onChange: (value: string) => void;
}

const PaymentCard = ({ value, selected, imageSrc, imageAlt, onChange }: PaymentCardProps) => (
  <Box
    onClick={() => onChange(value)}
    sx={{
      position: 'relative',
      width: '100%',
      maxWidth: '177px',
      height: '93px',
      border: '1px solid',
      borderColor: selected ? '#478CCF' : '#0000',
      borderRadius: 2,
      cursor: 'pointer',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      transition: 'border-color 0.2s ease',
      '&:hover': {
        borderColor: '#478CCF',
      },
    }}
  >
    {selected && (
      <Box
        sx={{
          position: 'absolute',
          top: 8,
          left: 8,
          color: '#242963',
        }}
      >
        <Icon icon="mdi:checkbox-marked-circle" width={24} height={24} />
      </Box>
    )}

    <Image src={imageSrc} alt={imageAlt} width={100} height={50} style={{ objectFit: 'fill' }} />

    <Radio checked={selected} value={value} sx={{ display: 'none' }} />
  </Box>
);

export default PaymentCard;
