'use client';

import { Icon } from '@iconify/react';
import { Dialog, DialogContent, Typography, Button, Stack, IconButton } from '@mui/material';
import { CloseIcon } from 'src/components/lightbox';
import { useLocales } from 'src/locales';

interface PaymentSuccessPopupProps {
  open: boolean;
  onClose: () => void;
  onViewOrder: () => void;
}

const PaymentSuccessPopup = ({ open, onClose, onViewOrder }: PaymentSuccessPopupProps) => {
  const { t } = useLocales();
  return (
    <Dialog open={open} onClose={onClose} maxWidth="xs" fullWidth>
      {/* Close Button */}
      <IconButton
        onClick={onClose}
        sx={{ position: 'absolute', top: 8, right: 8, color: 'grey.600' }}
      >
        <CloseIcon />
      </IconButton>

      {/* Icon & Title */}
      <DialogContent sx={{ textAlign: 'center', py: 4 }}>
        <Icon icon="mynaui:credit-card-check" width="24" height="24" />{' '}
        <Typography variant="h5" sx={{ fontWeight: 'bold', mt: 2 }}>
          {t('payment.payment_success')}
        </Typography>
        <Typography variant="body1" color="text.secondary">
          {t('profile.my_orders.order_details')}
        </Typography>
      </DialogContent>

      {/* Actions */}
      <Stack direction="row" justifyContent="center" spacing={2} pb={3}>
        <Button variant="contained" color="primary" onClick={onViewOrder}>
          {t('profile.my_orders.order_details')}
        </Button>
        <Button variant="outlined" color="secondary" onClick={onClose}>
          {t('common.close')}
        </Button>
      </Stack>
    </Dialog>
  );
};

export default PaymentSuccessPopup;
