import { StyledButton } from './styles';
import { IProps } from './types';

const CustomButton = ({ type, isPrimary, handleOnClick, children, sx }: IProps) => {
  const sxProp = { ...sx };
  return (
    <StyledButton
      type={type}
      sx={{
        borderRadius: '30px',
        bgcolor: (theme) => (isPrimary ? theme.palette.primary.main : theme.palette.secondary.main),
        color: (theme) => theme.palette.common.white,
        '&:hover': {
          bgcolor: (theme) =>
            !isPrimary ? theme.palette.primary.main : theme.palette.secondary.main,
        },
        ...sxProp,
      }}
      onClick={handleOnClick}
    >
      {children}
    </StyledButton>
  );
};
export default CustomButton;
