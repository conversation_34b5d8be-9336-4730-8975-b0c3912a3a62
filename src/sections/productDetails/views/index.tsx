'use client';

import { Box, Skeleton } from '@mui/material';
import ScrollToTopButton from 'src/components/scrollToTopButton';
import ProductDetails from 'src/components/product-details';
import BookProduct from 'src/components/product-info';
import Ratings from 'src/components/product-reviews/ratings';
import ListHomeCategory from 'src/components/list-category';
import PageHeader from 'src/components/page-header';
import { useGetProduct } from 'src/api/product';
import { useLocales } from 'src/locales';

export default function ProductInfo({ bookID }: { bookID: string }) {
  const { t } = useLocales();
  const { bookDetails, bookDetailsLoading } = useGetProduct(bookID);

  if (bookDetailsLoading || !bookDetails) {
    return (
      <Box
        sx={{
          maxWidth: '1639px',
          width: '100%',
        }}
      >
        <Skeleton variant="rectangular" height={60} />
        <Box py={6}>
          <Skeleton variant="rectangular" height={400} />
        </Box>
        <Box py={6}>
          <Skeleton variant="rectangular" height={300} />
        </Box>
        <Box py={6}>
          <Skeleton variant="rectangular" height={200} />
        </Box>
        <Box py={6}>
          <Skeleton variant="rectangular" height={400} />
        </Box>
      </Box>
    );
  }
  // TODO: add placeholder for images
  const { images = [], category, related_products } = bookDetails;
  const bookBriefDetails = {
    title: bookDetails.name,
    subtitle: bookDetails.alt_cover,
    author: bookDetails.author,
    rating: bookDetails.total_review_precent.rating,
    specifications: {
      weight: '( 710 غ )',
      packaging: 'مجلد كرتوناج',
      material: 'كرتون',
      pages: '( صفحة 512 )',
      dimensions: ' 7.8 × 5.2 × 4.4 سم',
    },
  };
  const thumbnails = (images || [])
    .map((image: string, index: number) => ({
      url: image,
      alt: `book image ${index}`,
    }))
    .slice(0, 4);
  const books = related_products?.items || [];
  const categoryName = category?.name || t('category');

  return (
    <Box
      sx={{
        maxWidth: '1639px',
        width: '100%',
      }}
    >
      <PageHeader title={categoryName} />
      <Box py={7}>
        <BookProduct
          gallery={thumbnails}
          rating={bookDetails.total_review_precent.rating}
          price={bookDetails.price_for_selling}
          title={bookDetails.name}
          author={bookDetails.author}
          book={bookDetails}
        />
      </Box>
      <Box py={6}>
        <ProductDetails productData={bookBriefDetails} />
      </Box>
      <Box py={6}>
        <Ratings bookID={bookID} />
      </Box>
      <Box py={6}>
        <ListHomeCategory
          category={{
            category_name: t('product_details.related_products'),
            products: books,
            category_id: category?.id,
          }}
        />
      </Box>
      <ScrollToTopButton />
    </Box>
  );
}
