// export interface IProductDetails {
//   id: number;
//   is_favourite: boolean;
//   image: string;
//   images: string[];
//   slug: string;
//   name: string;
//   category: ICategory;
//   cover: string;
//   author: string;
//   price_for_selling: number;
//   alt_image: string;
//   alt_cover: string;
//   short_description: string;
//   key_words: string;
//   robot: any;
//   has_discount: boolean;
//   discount: number;
//   price: number;
//   total_review_precent: ITotalReviewPrecent;
//   related_products: IRelatedProducts;
// }

// export interface ITotalReviewPrecent {
//   total_reviews: number;
//   rating: number;
//   stars_reviews: number[];
// }

// export interface IRelatedProducts {
//   title: ITitle;
//   items: IBookItemType[];
// }

// export interface ITitle {
//   value: string;
// }
