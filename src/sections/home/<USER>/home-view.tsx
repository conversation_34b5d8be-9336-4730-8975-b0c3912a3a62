'use client';

// @mui
import Box from '@mui/material/Box';
// layouts
import Skeleton from '@mui/material/Skeleton';
import MainLayout from 'src/layouts/main';
// components
import ListHomeCategory from 'src/components/list-category';
import ScrollToTopButton from 'src/components/scrollToTopButton';
import { useGetHome } from 'src/api/home';
import HomeHero from '../home-hero';
import HomeCategory from '../home-category';

// ----------------------------------------------------------------------

export default function HomeView() {
  const { homeData, isHomeLoading } = useGetHome();

  return (
    <MainLayout>
      <Box py={2}>
        {isHomeLoading ? (
          <Skeleton variant="rectangular" width="100%" height={400} />
        ) : (
          <HomeHero data={homeData.banners} />
        )}
      </Box>
      {isHomeLoading ? (
        <>
          {/* Category skeleton */}
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2, mb: 6 }}>
            {[...Array(8)].map((_, index) => (
              <Skeleton
                key={index}
                variant="rectangular"
                width={280}
                height={100}
                sx={{ borderRadius: 2 }}
              />
            ))}
          </Box>

          {/* Product lists skeleton */}
          {[...Array(3)].map((_, index) => (
            <Box my={6} key={index}>
              <Skeleton variant="text" width={200} height={40} sx={{ mb: 2 }} />
              <Box sx={{ display: 'flex', gap: 2, overflow: 'hidden' }}>
                {[...Array(4)].map((item, idx) => (
                  <Skeleton
                    key={idx}
                    variant="rectangular"
                    width={280}
                    height={400}
                    sx={{ borderRadius: 2 }}
                  />
                ))}
              </Box>
            </Box>
          ))}
        </>
      ) : (
        <>
          <HomeCategory categories={homeData?.categories} />
          {homeData?.products_by_category.map((category) => (
            <Box my={6} key={category.category_id}>
              <ListHomeCategory key={category.category_id} category={category} />
            </Box>
          ))}
        </>
      )}

      <ScrollToTopButton />
    </MainLayout>
  );
}
