import { m } from 'framer-motion';
// @mui
import { useTheme } from '@mui/material/styles';
import useMediaQuery from '@mui/material/useMediaQuery';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import { Card, CardContent, Paper } from '@mui/material';
// routes
import { MotionContainer, varFade } from 'src/components/animate';
import Carousel, { CarouselArrows, useCarousel } from 'src/components/carousel';
import Image from 'src/components/image';
import { pxToRem } from 'src/theme/typography';
import { Banner } from 'src/types/home';

// ----------------------------------------------------------------------

export default function HomeHero({ data }: { data: Banner[] }) {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm')); // hides arrows on "sm" and below

  const carousel = useCarousel({
    speed: 800,
    rtl: true,
  });

  return (
    <Card sx={{ maxHeight: '606px' }}>
      {!isMobile ? (
        <CarouselArrows
          filled
          icon="ph:arrow-right"
          onNext={carousel.onNext}
          onPrev={carousel.onPrev}
        >
          <Carousel ref={carousel.carouselRef} {...carousel.carouselSettings}>
            {data.map((item, index) => (
              <CarouselItem
                key={index}
                item={item}
                active={index === carousel.currentIndex}
                carousel={carousel}
                numberDots={data.length}
              />
            ))}
          </Carousel>
        </CarouselArrows>
      ) : (
        <Carousel ref={carousel.carouselRef} {...carousel.carouselSettings}>
          {data.map((item, index) => (
            <CarouselItem
              key={index}
              item={item}
              active={index === carousel.currentIndex}
              carousel={carousel}
              numberDots={data.length}
            />
          ))}
        </Carousel>
      )}
    </Card>
  );
}

// ----------------------------------------------------------------------

type CarouselItemProps = {
  item: Banner;
  active: boolean;
  carousel: ReturnType<typeof useCarousel>;
  numberDots: number;
};

function CarouselItem({ item, active, carousel, numberDots }: CarouselItemProps) {
  const theme = useTheme();
  const isRTL = theme.direction === 'rtl';

  const { title } = item;

  const variants = theme.direction === 'rtl' ? varFade().inLeft : varFade().inRight;

  return (
    <Paper sx={{ position: 'relative' }}>
      <Image dir="rtl" alt={title} src="assets/images/home/<USER>/hero_1.png" ratio="21/9" />

      {/* Overlay with linear gradient */}
      <Box
        sx={{
          top: 0,
          width: 1,
          height: 1,
          position: 'absolute',
          background: 'linear-gradient(to left top, #092D76, #242963)',
          opacity: 0.8, // Adjust opacity to your preference
        }}
      />

      <CardContent
        component={MotionContainer}
        animate={active}
        action
        sx={{
          top: 0,
          left: 0,
          width: '50%',
          height: { xs: '100%', xl: '80%'},
          position: 'absolute',
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'end',
          justifyContent: 'center',
          textAlign: 'left', // Align text to the center
          color: 'common.white',
          zIndex: 1, // Make sure content is above the overlay
          ml: { xs: 1, sm: 10 },
          mb: 3,
        }}
      >
        <Box
          sx={{
            position: 'absolute',
            width: '100%',
            height: '100%',
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
          }}
        >
          <m.div
            variants={variants}
            initial="initial"
            animate="animate"
            exit="exit"
            transition={{
              duration: 0.5,
              delay: 0.2,
            }}
          >
            <Typography
              variant="h2"
              sx={{
                fontSize: {
                  xs: pxToRem(18), // for extra-small screens
                  sm: pxToRem(56), // for small screens
                  md: pxToRem(72), // for medium screens
                  lg: pxToRem(86), // for large screens
                },
              }}
              gutterBottom
            >
              {title}
            </Typography>
          </m.div>

          <m.div
            variants={variants}
            initial="initial"
            animate="animate"
            exit="exit"
            transition={{
              duration: 0.5,
              delay: 0.4,
            }}
          >
            <Typography
              variant="body1"
              sx={{
                fontSize: {
                  xs: pxToRem(20), // for extra-small screens
                  sm: pxToRem(28), // for small screens
                  md: pxToRem(36), // for medium screens
                  lg: pxToRem(44), // for large screens
                },
              }}
            >
              {item.description}
            </Typography>
          </m.div>

          {/* Indicators - hidden on mobile */}
          <Box
            sx={{
              display: { xs: 'none', sm: 'flex' },
              justifyContent: isRTL ? 'end' : 'start',
              gap: 1,
              mt: 3,
            }}
          >
            {[...Array(numberDots)].map((_, index) => (
              <Box
                key={index}
                sx={{
                  width: index === carousel.currentIndex ? 30 : 40,
                  height: 8,
                  borderRadius: '50px',
                  backgroundColor:
                    index === carousel.currentIndex
                      ? theme.palette.secondary.main
                      : theme.palette.common.white,
                  transition: 'all 0.3s ease-in-out',
                  cursor: 'pointer',
                }}
                onClick={() => {
                  carousel.onTogo(index);
                }}
              />
            ))}
          </Box>
        </Box>
      </CardContent>
    </Paper>
  );
}
