import { Box, Stack, Typography, Skeleton } from '@mui/material';
import { useRouter } from 'next/navigation';
import SvgColor from 'src/components/svg-color';
import { ICategory } from 'src/types/shared';
import { ICONS_CATEGORIES } from 'src/utils/constant';

function HomeCategory({ categories }: { categories: ICategory[] }) {
  const router = useRouter();

  if (!categories) {
    return (
      <Box sx={{ position: 'relative' }}>
        {[...Array(4)].map((_, index) => (
          <>
            <Skeleton key={index + 1} variant="rounded" width={250} height={40} animation="wave" />
            <Stack
              key={index}
              direction="row"
              alignItems="center"
              sx={{
                overflow: 'hidden',
                pb: 1,
                gap: 2,
                position: 'relative',
              }}
            >
              <Box
                sx={{
                  width: '100%',
                  position: 'relative',
                  display: 'flex',
                  py: 4,
                  justifyContent: 'space-between',
                  gap: 2,
                  overflowX: 'auto',
                }}
              >
                {[...Array(4)].map((__, indexCard) => (
                  <Box
                    key={indexCard}
                    sx={{
                      display: 'flex',
                      flexDirection: 'column',
                      alignItems: 'center',
                      justifyContent: 'center',
                      gap: 2,
                      height: '170px',
                      width: '170px',
                      flex: '0 0 auto',
                    }}
                  >
                    <Skeleton variant="rounded" width={170} height={170} animation="wave" />
                  </Box>
                ))}
              </Box>
            </Stack>
          </>
        ))}
      </Box>
    );
  }

  return (
    <Box sx={{ position: 'relative' }}>
      <Stack
        direction="row"
        alignItems="center"
        sx={{
          overflow: 'hidden',
          pb: 1,
          gap: 2,
          position: 'relative',
        }}
      >
        <Box
          sx={{
            width: '100%',
            position: 'relative',
            display: 'flex',
            py: 4,
            justifyContent: 'space-between',
            gap: 2,
            overflowX: 'auto',
            '&::-webkit-scrollbar': {
              display: 'none',
            },
            '-ms-overflow-style': 'none',
          }}
        >
          {categories.map((item, index) => (
            <Box
              key={item.id}
              onClick={() => router.push(`/category/${item.id}`)}
              sx={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center',
                gap: 2,
                borderRadius: '25px',
                bgcolor: ICONS_CATEGORIES[index].bgColor,
                height: '170px',
                width: '170px',
                flex: '0 0 auto',
                cursor: 'pointer',
                transition: 'transform 0.2s',
                '&:hover': {
                  transform: 'scale(1.05)',
                },
                '@media (max-width:400px)': {
                  height: '120px',
                  width: '120px',
                },
              }}
            >
              <SvgColor
                sx={{
                  color: ICONS_CATEGORIES[index].subtitleColor,
                  width: '70px',
                  height: '70px',
                  '@media (max-width:400px)': {
                    width: '50px',
                    height: '50px',
                  },
                }}
                src={`/assets/icons/home/<USER>/${ICONS_CATEGORIES[index].icon}.svg`}
              />

              <Typography
                variant="button"
                sx={{
                  textAlign: 'center',
                  width: '80%',
                  color: ICONS_CATEGORIES[index].subtitleColor,
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  display: '-webkit-box',
                  WebkitLineClamp: 2,
                  WebkitBoxOrient: 'vertical',
                  '@media (max-width:400px)': {
                    fontSize: '0.8rem',
                  },
                }}
              >
                {item.name}
              </Typography>
            </Box>
          ))}
        </Box>
      </Stack>
    </Box>
  );
}

export default HomeCategory;
