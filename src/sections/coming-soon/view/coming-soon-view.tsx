'use client';

import { useState, useEffect } from 'react';
import { m } from 'framer-motion';
// @mui
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Typography from '@mui/material/Typography';
import TextField from '@mui/material/TextField';
import Stack from '@mui/material/Stack';
import Container from '@mui/material/Container';
import Paper from '@mui/material/Paper';
import Grid from '@mui/material/Grid';
import IconButton from '@mui/material/IconButton';
import { alpha, useTheme } from '@mui/material/styles';
// icons
// layouts
// assets
import { ComingSoonIllustration } from 'src/assets/illustrations';
// components
import { RouterLink } from 'src/routes/components';
import { MotionContainer, varBounce } from 'src/components/animate';
import Iconify from 'src/components/iconify';

// ----------------------------------------------------------------------

interface TimeLeft {
  days: number;
  hours: number;
  minutes: number;
  seconds: number;
}

export default function ComingSoonView() {
  const theme = useTheme();
  const [email, setEmail] = useState('');
  const [timeLeft, setTimeLeft] = useState<TimeLeft>({
    days: 0,
    hours: 0,
    minutes: 0,
    seconds: 0,
  });

  // Set target date (you can modify this to your desired launch date)
  const targetDate = new Date('2024-12-31T00:00:00').getTime();

  useEffect(() => {
    const timer = setInterval(() => {
      const now = new Date().getTime();
      const difference = targetDate - now;

      if (difference > 0) {
        setTimeLeft({
          days: Math.floor(difference / (1000 * 60 * 60 * 24)),
          hours: Math.floor((difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)),
          minutes: Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60)),
          seconds: Math.floor((difference % (1000 * 60)) / 1000),
        });
      }
    }, 1000);

    return () => clearInterval(timer);
  }, [targetDate]);

  const handleEmailSubmit = () => {
    // Handle email subscription logic here
    console.log('Email submitted:', email);
    setEmail('');
  };

  return (
    <Container maxWidth="md">
      <MotionContainer>
        <m.div variants={varBounce().in}>
          <Typography variant="h2" sx={{ mb: 2, textAlign: 'center' }}>
            Coming Soon!
          </Typography>
        </m.div>

        <m.div variants={varBounce().in}>
          <Typography
            variant="h6"
            sx={{
              color: 'text.secondary',
              textAlign: 'center',
              mb: 5,
              maxWidth: 480,
              mx: 'auto',
            }}
          >
            We&apos;re working hard to bring you something amazing. Stay tuned for updates!
          </Typography>
        </m.div>

        <m.div variants={varBounce().in}>
          <ComingSoonIllustration
            sx={{
              height: 240,
              my: { xs: 5, sm: 8 },
              mx: 'auto',
              display: 'block',
            }}
          />
        </m.div>

        {/* Countdown Timer */}
        <m.div variants={varBounce().in}>
          <Box sx={{ mb: 6 }}>
            <Typography variant="h5" sx={{ textAlign: 'center', mb: 3 }}>
              Launch Countdown
            </Typography>
            <Grid container spacing={2} justifyContent="center">
              {[
                { label: 'Days', value: timeLeft.days },
                { label: 'Hours', value: timeLeft.hours },
                { label: 'Minutes', value: timeLeft.minutes },
                { label: 'Seconds', value: timeLeft.seconds },
              ].map((item) => (
                <Grid item key={item.label}>
                  <Paper
                    sx={{
                      p: 2,
                      textAlign: 'center',
                      minWidth: 80,
                      bgcolor: alpha(theme.palette.primary.main, 0.08),
                      border: `1px solid ${alpha(theme.palette.primary.main, 0.24)}`,
                    }}
                  >
                    <Typography variant="h4" sx={{ color: 'primary.main', fontWeight: 'bold' }}>
                      {item.value.toString().padStart(2, '0')}
                    </Typography>
                    <Typography variant="caption" sx={{ color: 'text.secondary' }}>
                      {item.label}
                    </Typography>
                  </Paper>
                </Grid>
              ))}
            </Grid>
          </Box>
        </m.div>

        <m.div variants={varBounce().in}>
          <Box
            sx={{
              p: 4,
              borderRadius: 2,
              bgcolor: alpha(theme.palette.grey[500], 0.04),
              border: `1px solid ${alpha(theme.palette.grey[500], 0.12)}`,
              textAlign: 'center',
              mb: 5,
            }}
          >
            <Typography variant="h6" sx={{ mb: 2 }}>
              Get Notified When We Launch
            </Typography>
            <Typography variant="body2" sx={{ color: 'text.secondary', mb: 3 }}>
              Be the first to know when we go live. Enter your email below.
            </Typography>

            <Stack
              direction={{ xs: 'column', sm: 'row' }}
              spacing={2}
              sx={{ maxWidth: 400, mx: 'auto' }}
            >
              <TextField
                fullWidth
                placeholder="Enter your email"
                variant="outlined"
                size="medium"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    bgcolor: 'background.paper',
                  },
                }}
              />
              <Button
                variant="contained"
                size="large"
                onClick={handleEmailSubmit}
                disabled={!email}
                sx={{
                  minWidth: { xs: '100%', sm: 120 },
                  height: 56,
                }}
              >
                Notify Me
              </Button>
            </Stack>
          </Box>
        </m.div>

        {/* Social Media Links */}
        <m.div variants={varBounce().in}>
          <Box sx={{ textAlign: 'center', mb: 5 }}>
            <Typography variant="h6" sx={{ mb: 2 }}>
              Follow Us
            </Typography>
            <Stack direction="row" spacing={2} justifyContent="center">
              <IconButton
                sx={{
                  bgcolor: alpha(theme.palette.primary.main, 0.08),
                  color: 'primary.main',
                  '&:hover': {
                    bgcolor: alpha(theme.palette.primary.main, 0.16),
                  },
                }}
              >
                <Iconify icon="eva:facebook-fill" />
              </IconButton>
              <IconButton
                sx={{
                  bgcolor: alpha(theme.palette.primary.main, 0.08),
                  color: 'primary.main',
                  '&:hover': {
                    bgcolor: alpha(theme.palette.primary.main, 0.16),
                  },
                }}
              >
                <Iconify icon="eva:twitter-fill" />
              </IconButton>
              <IconButton
                sx={{
                  bgcolor: alpha(theme.palette.primary.main, 0.08),
                  color: 'primary.main',
                  '&:hover': {
                    bgcolor: alpha(theme.palette.primary.main, 0.16),
                  },
                }}
              >
                <Iconify icon="eva:linkedin-fill" />
              </IconButton>
              <IconButton
                sx={{
                  bgcolor: alpha(theme.palette.primary.main, 0.08),
                  color: 'primary.main',
                  '&:hover': {
                    bgcolor: alpha(theme.palette.primary.main, 0.16),
                  },
                }}
              >
                <Iconify icon="eva:instagram-fill" />
              </IconButton>
            </Stack>
          </Box>
        </m.div>

        <m.div variants={varBounce().in}>
          <Stack direction="row" spacing={2} justifyContent="center">
            <Button component={RouterLink} href="/" size="large" variant="outlined">
              Back to Home
            </Button>
            <Button component={RouterLink} href="/contact-us" size="large" variant="contained">
              Contact Us
            </Button>
          </Stack>
        </m.div>
      </MotionContainer>
    </Container>
  );
}
