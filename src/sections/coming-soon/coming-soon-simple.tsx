'use client';

import { m } from 'framer-motion';
// @mui
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Typography from '@mui/material/Typography';
import Container from '@mui/material/Container';
// layouts
import CompactLayout from 'src/layouts/compact';
// assets
import { ComingSoonIllustration } from 'src/assets/illustrations';
// components
import { RouterLink } from 'src/routes/components';
import { MotionContainer, varBounce } from 'src/components/animate';
import { useLocales } from 'src/locales';

// ----------------------------------------------------------------------

export default function ComingSoonSimple() {
  const { t } = useLocales();

  return (
    <CompactLayout>
      <Container maxWidth="sm">
        <MotionContainer>
          <m.div variants={varBounce().in}>
            <Typography variant="h2" sx={{ mb: 2, textAlign: 'center' }}>
              {t('coming_soon.coming_soon')}
            </Typography>
          </m.div>

          <m.div variants={varBounce().in}>
            <Typography
              variant="h6"
              sx={{
                color: 'text.secondary',
                textAlign: 'center',
                mb: 5,
                maxWidth: 480,
                mx: 'auto',
              }}
            >
              {t('coming_soon.description')}
            </Typography>
          </m.div>

          <m.div variants={varBounce().in}>
            <ComingSoonIllustration
              sx={{
                height: 240,
                my: { xs: 5, sm: 8 },
                mx: 'auto',
                display: 'block',
              }}
            />
          </m.div>

          {/* <m.div variants={varBounce().in}>
            <Box
              sx={{
                p: 3,
                borderRadius: 2,
                bgcolor: alpha(theme.palette.grey[500], 0.04),
                border: `1px solid ${alpha(theme.palette.grey[500], 0.12)}`,
                textAlign: 'center',
                mb: 5,
              }}
            >
              <Typography variant="h6" sx={{ mb: 1 }}>
                Something Exciting is Coming
              </Typography>
              <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                We&apos;re putting the finishing touches on our new experience.
              </Typography>
            </Box>
          </m.div> */}

          <m.div variants={varBounce().in}>
            <Box sx={{ textAlign: 'center' }}>
              <Button
                component={RouterLink}
                href="/"
                size="large"
                variant="contained"
                sx={{ mr: 2 }}
              >
                {t('coming_soon.back_to_home')}
              </Button>
              <Button component={RouterLink} href="/contact-us" size="large" variant="outlined">
                {t('coming_soon.contact_us')}{' '}
              </Button>
            </Box>
          </m.div>
        </MotionContainer>
      </Container>
    </CompactLayout>
  );
}
