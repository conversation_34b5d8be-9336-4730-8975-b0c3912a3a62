# Coming Soon Page

This section contains the coming soon page components for the Dar Alfajer application.

## Components

### ComingSoonView
The main coming soon page with full features including:
- Animated countdown timer
- Email subscription form
- Social media links
- Beautiful illustrations
- Responsive design

### ComingSoonSimple
A simplified version of the coming soon page with:
- Basic message and illustration
- Navigation buttons
- Minimal design

## Features

### Countdown Timer
- Displays days, hours, minutes, and seconds until launch
- Automatically updates every second
- Customizable target date

### Email Subscription
- Collects user emails for launch notifications
- Form validation
- Responsive design

### Social Media Integration
- Facebook, Twitter, LinkedIn, Instagram links
- Hover effects
- Consistent styling

### Animations
- Framer Motion animations
- Bounce effects
- Smooth transitions

## Usage

```tsx
import { ComingSoonView, ComingSoonSimple } from 'src/sections/coming-soon';

// Full featured version
<ComingSoonView />

// Simple version
<ComingSoonSimple />
```

## Customization

### Countdown Timer
To change the target date, modify the `targetDate` variable in `coming-soon-view.tsx`:

```tsx
const targetDate = new Date('2024-12-31T00:00:00').getTime();
```

### Social Media Links
Update the social media icons and links in the social media section.

### Styling
All components use Material-UI theming and can be customized through the theme provider.

## File Structure

```
src/sections/coming-soon/
├── view/
│   └── coming-soon-view.tsx    # Main coming soon page
├── coming-soon-simple.tsx      # Simple version
├── index.ts                    # Exports
└── README.md                   # Documentation
```
