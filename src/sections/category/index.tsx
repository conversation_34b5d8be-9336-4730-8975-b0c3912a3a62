'use client';

import React, { useEffect, useState } from 'react';
import { Box, Grid, Typography, Select, MenuItem, SelectChangeEvent } from '@mui/material';
import { styled } from '@mui/system';
import Skeleton from '@mui/material/Skeleton';
import ProductCard from 'src/components/product-card-item';
import PageHeader from 'src/components/page-header';
import { IProductItem } from 'src/types/product';
import { useGetProductByCategory } from 'src/api/product';
import { useLocales } from 'src/locales';

const CustomSelect = styled(Select)({
  minWidth: 120,
  marginLeft: 8,
  width: '191px',
  height: '42px',
  border: '1px solid #478CCF',
  borderRadius: '30px',
});

export default function ProductsPage({ categoryId }: { categoryId: string }) {
  const { products_by_category, isLoadingProduct } = useGetProductByCategory(categoryId);
  const [sortOrder, setSortOrder] = useState<string>('asc');
  const [sortedData, setSortedData] = useState<IProductItem[]>([]);
  const products = products_by_category?.products;
  const categoryName = products_by_category?.category_name;
  const { t } = useLocales();

  useEffect(() => {
    if (!products) return;

    const sorted: IProductItem[] = [...products];
    if (sortOrder === 'asc') {
      sorted.sort((a, b) => Number(a.price_for_selling) - Number(b.price_for_selling));
    } else if (sortOrder === 'desc') {
      sorted.sort((a, b) => Number(b.price_for_selling) - Number(a.price_for_selling));
    }
    setSortedData(sorted);
  }, [sortOrder, products]);

  if (!products_by_category && isLoadingProduct) {
    return (
      <Box sx={{ width: '100%', display: 'flex', justifyContent: 'center', padding: 2 }}>
        <Box sx={{ width: '1640px' }}>
          {/* Header Skeleton */}
          <Skeleton variant="text" width={300} height={60} sx={{ mb: 4 }} />

          {/* Sort Section Skeleton */}
          <Box sx={{ display: 'flex', alignItems: 'center', mt: '47px', mb: '50px' }}>
            <Skeleton variant="text" width={120} height={40} />
            <Skeleton
              variant="rectangular"
              width={191}
              height={42}
              sx={{ ml: 1, borderRadius: '30px' }}
            />
          </Box>

          {/* Products Grid Skeleton */}
          <Grid container spacing={2}>
            {[...Array(12)].map((_, index) => (
              <Grid item xs={12} sm={6} md={4} lg={2.4} key={index}>
                <Skeleton
                  variant="rectangular"
                  width="100%"
                  height={300}
                  sx={{ borderRadius: 2 }}
                />
              </Grid>
            ))}
          </Grid>
        </Box>
      </Box>
    );
  }

  const handleSortChange = (event: SelectChangeEvent<unknown>) => {
    setSortOrder(event.target.value as string);
  };

  return (
    <Box sx={{ width: '100%', display: 'flex', justifyContent: 'center', padding: 2 }}>
      <Box sx={{ width: '1640px' }}>
        <PageHeader title={categoryName} />

        {/* Sort Section */}
        <Box sx={{ display: 'flex', alignItems: 'center', mt: '47px', mb: '50px' }}>
          <Typography sx={{ fontSize: '24px' }}>{t('filter.sort_by_price')}</Typography>
          <CustomSelect
            value={sortOrder}
            onChange={handleSortChange}
            defaultValue="asc"
            size="small"
            disableUnderline
          >
            <MenuItem value="none">{t('filter.sort_by_price_none')}</MenuItem>
            <MenuItem value="asc" color="#478CCF">
              {t('filter.asc')}
            </MenuItem>
            <MenuItem value="desc">{t('filter.desc')}</MenuItem>
          </CustomSelect>
        </Box>

        {/* Product List */}
        {products?.length === 0 ? (
          <Box sx={{ width: '100%', display: 'flex', justifyContent: 'center', padding: 2 }}>
            <Typography sx={{ fontSize: '24px' }}>
              لا توجد منتجات في هذه الفئة حالياً، يرجى المحاولة مرة أخرى لاحقاً
            </Typography>
          </Box>
        ) : (
          <Grid
            container
            spacing={2}
            sx={{
              '@media (max-width:450px)': {
                justifyItems: 'center',
              },
            }}
          >
            {sortedData.map((product) => (
              <Grid
                item
                xs={6}
                sm={6}
                md={4}
                lg={2.4}
                key={product.id}
                sx={{
                  '@media (max-width:450px)': {
                    flexBasis: '100%',
                    maxWidth: '100%',
                  },
                }}
              >
                <ProductCard isCategory category={product.category.name} product={product} />
              </Grid>
            ))}
          </Grid>
        )}
      </Box>
    </Box>
  );
}
