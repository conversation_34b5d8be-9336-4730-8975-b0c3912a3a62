import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { usePaymentStore } from 'src/store/payment/ysePaymentStore';
import { PaymentFormData } from './types';
import { createPaymentSchema } from './validation-schema';

export const usePaymentForm = () => {
  const { formData, setFormData } = usePaymentStore();
  const schema = createPaymentSchema();

  const methods = useForm<PaymentFormData>({
    resolver: yupResolver(schema),
    defaultValues: formData,
    mode: 'onChange',
  });

  const onSubmit = (data: PaymentFormData) => {
    setFormData(data);
    console.log('Payment submitted:', data);
  };

  return {
    methods,
    onSubmit,
  };
};
