import { Grid, TextField } from '@mui/material';
import InputTextField from 'src/components/text-field';

interface PaymentFormsProps {
  type: 'card' | 'paypal';
}

const PaymentForms = ({ type }: PaymentFormsProps) => {
  if (type === 'card') {
    return (
      <Grid container spacing={3}>
        <Grid item xs={12} sm={6}>
          <InputTextField
            label="رقم البطاقة"
            maxWidth="389px"
            placeholder="1234 5678 9012 3456"
            maxHeight="50px"
          />
          {/* <TextField fullWidth label="رقم البطاقة" placeholder="1234 5678 9012 3456" /> */}
        </Grid>
        <Grid item xs={12} sm={6}>
          <InputTextField
            label="اسم صاحب البطاقة"
            maxWidth="389px"
            placeholder="إدخال الاسم هنا"
            maxHeight="50px"
          />
          {/* <TextField fullWidth label="اسم صاحب البطاقة" placeholder="إدخال الاسم هنا" /> */}
        </Grid>
        <Grid item xs={12} sm={6}>
          <TextField
            fullWidth
            type="date"
            label="تاريخ انتهاء البطاقة"
            InputLabelProps={{ shrink: true }}
          />
        </Grid>
        <Grid item xs={12} sm={6}>
          <InputTextField label="CCV" maxWidth="389px" placeholder="123" maxHeight="50px" />
          {/* <TextField fullWidth label="CCV" placeholder="123" inputProps={{ maxLength: 3 }} /> */}
        </Grid>
      </Grid>
    );
  }

  return (
    <Grid container spacing={3}>
      <Grid item xs={12}>
        <TextField
          fullWidth
          type="email"
          label="البريد الإلكتروني PayPal"
          placeholder="<EMAIL>"
        />
      </Grid>
      <Grid item xs={12}>
        <TextField fullWidth label="رقم الهاتف" placeholder="05xxxxxxxx" />
      </Grid>
    </Grid>
  );
};

export default PaymentForms;
