// import { useState } from 'react';
// import {
//   Box,
//   Typography,
//   RadioGroup,
//   FormControlLabel,
//   Radio,
//   TextField,
//   Grid,
// } from '@mui/material';
// import Image from 'next/image';

// const PaymentMethod = () => {
//   const [paymentMethod, setPaymentMethod] = useState('mastercard');

//   return (
//     <Box sx={{ mt: 4 }}>
//       <Typography variant="h5" sx={{ mb: 3, fontWeight: 'bold' }}>
//         طريقة الدفع
//       </Typography>

//       <RadioGroup value={paymentMethod} onChange={(e) => setPaymentMethod(e.target.value)}>
//         <Box sx={{ display: 'flex', gap: 2, mb: 3 }}>
//           <FormControlLabel
//             value="mastercard"
//             control={<Radio />}
//             label={
//               <Box component="span" sx={{ display: 'flex', alignItems: 'center' }}>
//                 <Image
//                   src="/assets/images/payment/mastercard.png"
//                   alt="Mastercard"
//                   width={60}
//                   height={40}
//                 />
//               </Box>
//             }
//           />
//           <FormControlLabel
//             value="visa"
//             control={<Radio />}
//             label={
//               <Box component="span" sx={{ display: 'flex', alignItems: 'center' }}>
//                 <Image src="/assets/images/payment/visa.png" alt="Visa" width={60} height={40} />
//               </Box>
//             }
//           />
//           <FormControlLabel
//             value="paypal"
//             control={<Radio />}
//             label={
//               <Box component="span" sx={{ display: 'flex', alignItems: 'center' }}>
//                 <Image
//                   src="/assets/images/payment/paypal-logo.png"
//                   alt="PayPal"
//                   width={60}
//                   height={40}
//                 />
//               </Box>
//             }
//           />
//         </Box>
//       </RadioGroup>

//       {(paymentMethod === 'mastercard' || paymentMethod === 'visa') && (
//         <Grid container spacing={3}>
//           <Grid item xs={12} sm={6}>
//             <TextField fullWidth label="رقم البطاقة" placeholder="1234 5678 9012 3456" />
//           </Grid>
//           <Grid item xs={12} sm={6}>
//             <TextField fullWidth label="اسم صاحب البطاقة" placeholder="إدخال الاسم هنا" />
//           </Grid>
//           <Grid item xs={12} sm={6}>
//             <TextField
//               fullWidth
//               type="date"
//               label="تاريخ انتهاء البطاقة"
//               InputLabelProps={{ shrink: true }}
//             />
//           </Grid>
//           <Grid item xs={12} sm={6}>
//             <TextField fullWidth label="CCV" placeholder="123" inputProps={{ maxLength: 3 }} />
//           </Grid>
//         </Grid>
//       )}

//       {paymentMethod === 'paypal' && (
//         <Grid container spacing={3}>
//           <Grid item xs={12}>
//             <TextField
//               fullWidth
//               type="email"
//               label="البريد الإلكتروني PayPal"
//               placeholder="<EMAIL>"
//             />
//           </Grid>
//           <Grid item xs={12}>
//             <TextField fullWidth label="رقم الهاتف" placeholder="05xxxxxxxx" />
//           </Grid>
//         </Grid>
//       )}
//     </Box>
//   );
// };

// export default PaymentMethod;

import { Box, Typography, RadioGroup } from '@mui/material';
import PaymentCard from 'src/components/checkout/papyment/payment-card';
import { usePaymentMethod } from './usePaymentMethod';
import PaymentForms from './payment-form';
import { PAYMENT_METHODS } from './constants';

const PaymentMethod = () => {
  const { paymentMethod, handlePaymentMethodChange } = usePaymentMethod();

  return (
    <Box sx={{ mt: 4 }}>
      <Typography variant="h5" sx={{ mb: 3, fontWeight: 'bold' }}>
        طريقة الدفع
      </Typography>

      <RadioGroup value={paymentMethod} onChange={(e) => handlePaymentMethodChange(e.target.value)}>
        <Box
          sx={{
            display: 'flex',
            gap: 2,
            mb: 3,
            flexWrap: 'wrap',
          }}
        >
          {PAYMENT_METHODS.map((method) => (
            <PaymentCard
              key={method.value}
              value={method.value}
              selected={paymentMethod === method.value}
              imageSrc={method.image}
              imageAlt={method.alt}
              onChange={handlePaymentMethodChange}
            />
          ))}
        </Box>
      </RadioGroup>

      <PaymentForms type={paymentMethod === 'paypal' ? 'paypal' : 'card'} />
    </Box>
  );
};

export default PaymentMethod;
