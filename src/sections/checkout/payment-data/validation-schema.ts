import * as Yup from 'yup';
import { PaymentMethodType } from './constants';

export const personalInfoSchema = Yup.object({
  firstName: Yup.string().required('الاسم الأول مطلوب'),
  lastName: Yup.string().required('الاسم الثاني مطلوب'),
  email: Yup.string().email('البريد الإلكتروني غير صحيح').required('البريد الإلكتروني مطلوب'),
  phone: Yup.string()
    .matches(/^[0-9]+$/, 'يجب أن يحتوي رقم الهاتف على أرقام فقط')
    .required('رقم الهاتف مطلوب'),
  city: Yup.string().required('المدينة مطلوبة'),
  district: Yup.string().required('الحي مطلوب'),
});

export const PaymentFormDataSchema = Yup.object({
  cardNumber: Yup.string().when('paymentMethod', {
    is: (val: string) => ['mastercard', 'visa'].includes(val),
    then: () => Yup.string().required('رقم البطاقة مطلوب'),
  }),
  cardHolder: Yup.string().when('paymentMethod', {
    is: (val: string) => ['mastercard', 'visa'].includes(val),
    then: () => Yup.string().required('اسم صاحب البطاقة مطلوب'),
  }),
  expiryDate: Yup.string().when('paymentMethod', {
    is: (val: string) => ['mastercard', 'visa'].includes(val),
    then: () => Yup.string().required('تاريخ انتهاء البطاقة مطلوب'),
  }),
  cvv: Yup.string().when('paymentMethod', {
    is: (val: string) => ['mastercard', 'visa'].includes(val),
    then: () => Yup.string().required('CVV مطلوب'),
  }),
  paypalEmail: Yup.string().when('paymentMethod', {
    is: 'paypal',
    then: () =>
      Yup.string().email('البريد الإلكتروني غير صحيح').required('البريد الإلكتروني مطلوب'),
  }),
  paypalPhone: Yup.string().when('paymentMethod', {
    is: 'paypal',
    then: () => Yup.string().required('رقم الهاتف مطلوب'),
  }),
});

export const baseSchema = Yup.object().shape({
  paymentMethod: Yup.string()
    .oneOf<PaymentMethodType>(['mastercard', 'visa', 'paypal'], 'طريقة دفع غير صالحة')
    .required('طريقة الدفع مطلوبة'),
});

const cardFields = {
  cardNumber: Yup.string().required('رقم البطاقة مطلوب'),
  cardHolder: Yup.string().required('اسم صاحب البطاقة مطلوب'),
  expiryDate: Yup.string().required('تاريخ انتهاء البطاقة مطلوب'),
  cvv: Yup.string().required('CVV مطلوب').length(3, 'يجب أن يكون 3 أرقام'),
};

const paypalFields = {
  paypalEmail: Yup.string().email('البريد الإلكتروني غير صحيح').required('البريد الإلكتروني مطلوب'),
  paypalPhone: Yup.string().required('رقم الهاتف مطلوب'),
};

export const createPaymentSchema = () =>
  Yup.object()
    .shape({
      paymentMethod: Yup.string()
        .oneOf(['mastercard', 'visa', 'paypal'] as PaymentMethodType[], 'طريقة دفع غير صالحة')
        .required('طريقة الدفع مطلوبة'),
      ...cardFields,
      ...paypalFields,
    })
    .test('conditional-validation', '', function (values) {
      if (!values) return true;

      if (['mastercard', 'visa'].includes(values.paymentMethod as string)) {
        const cardValidation = Yup.object().shape(cardFields);
        try {
          cardValidation.validateSync({
            cardNumber: values.cardNumber,
            cardHolder: values.cardHolder,
            expiryDate: values.expiryDate,
            cvv: values.cvv,
          });
          return true;
        } catch (err) {
          return this.createError({ message: (err as Error).message });
        }
      }

      if (values.paymentMethod === 'paypal') {
        const paypalValidation = Yup.object().shape(paypalFields);
        try {
          paypalValidation.validateSync({
            paypalEmail: values.paypalEmail,
            paypalPhone: values.paypalPhone,
          });
          return true;
        } catch (err) {
          return this.createError({ message: (err as Error).message });
        }
      }

      return true;
    });
