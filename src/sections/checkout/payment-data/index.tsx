'use client';

import { Box, Divider, Typography } from '@mui/material';
import { useLocales } from 'src/locales';
import PersonalInfo from './personal-info';
import PaymentMethod from './payment-method';

const PaymentData = () => {
  const { t } = useLocales();
  return (
    <Box>
      <Box sx={{ bgcolor: 'background.paper', borderRadius: '30px', p: '35px' }}>
        <Typography variant="h4" sx={{ mb: 4, fontWeight: 'bold' }}>
          {t('payment.payment_data')}
        </Typography>
        <PersonalInfo />
        <Divider />
        <PaymentMethod />
      </Box>
    </Box>
  );
};

export default PaymentData;
