import { useState } from 'react';
import { FieldState } from 'src/types/field-state';
import { IUser } from 'src/auth/types';
import { personalInfoSchema } from './validation-schema';

type CheckoutPersonalInfo = Pick<
  IUser,
  'id' | 'first_name' | 'last_name' | 'email' | 'phone' | 'city' | 'district'
>;

export const usePersonalInfo = () => {
  const [fields, setFields] = useState<Record<keyof CheckoutPersonalInfo, FieldState>>({
    id: { value: '', error: '', touched: false },
    first_name: { value: '', error: '', touched: false },
    last_name: { value: '', error: '', touched: false },
    email: { value: '', error: '', touched: false },
    phone: { value: '', error: '', touched: false },
    city: { value: '', error: '', touched: false },
    district: { value: '', error: '', touched: false },
  });

  const validateField = (name: keyof CheckoutPersonalInfo, value: string): string => {
    try {
      personalInfoSchema.validateSyncAt(name, { [name]: value });
      return '';
    } catch (error) {
      if (error instanceof Error) {
        return error.message;
      }
      return 'حدث خطأ';
    }
  };

  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = event.target;
    const fieldName = name as keyof CheckoutPersonalInfo;

    setFields((prev) => ({
      ...prev,
      [fieldName]: {
        ...prev[fieldName],
        value,
        error: prev[fieldName].touched ? validateField(fieldName, value) : '',
      },
    }));
  };

  const handleBlur = (event: React.FocusEvent<HTMLInputElement>) => {
    const { name, value } = event.target;
    const fieldName = name as keyof CheckoutPersonalInfo;

    setFields((prev) => ({
      ...prev,
      [fieldName]: {
        ...prev[fieldName],
        touched: true,
        error: validateField(fieldName, value),
      },
    }));
  };

  return {
    fields,
    handleChange,
    handleBlur,
  };
};
