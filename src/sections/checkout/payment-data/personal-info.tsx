import { Grid, TextField } from '@mui/material';
import { usePersonalInfo } from './usePersonalInfo';

const PersonalInfo = () => {
  const { fields, handleChange, handleBlur } = usePersonalInfo();

  return (
    <Grid container spacing={3}>
      <Grid item xs={12} sm={6}>
        <TextField
          fullWidth
          name="firstName"
          placeholder="إدخال الاسم هنا"
          label="الاسم الأول"
          value={fields.first_name.value}
          onChange={handleChange}
          onBlur={handleBlur}
          error={fields.first_name.touched && Boolean(fields.first_name.error)}
          helperText={fields.first_name.touched && fields.first_name.error}
        />
      </Grid>
      <Grid item xs={12} sm={6}>
        <TextField
          fullWidth
          name="lastName"
          placeholder="إدخال الاسم هنا"
          label="الاسم الثاني"
          value={fields.last_name.value}
          onChange={handleChange}
          onBlur={handleBlur}
          error={fields.last_name.touched && Boolean(fields.last_name.error)}
          helperText={fields.last_name.touched && fields.last_name.error}
        />
      </Grid>
      <Grid item xs={12} sm={6}>
        <TextField
          fullWidth
          name="email"
          type="email"
          placeholder="إدخال البريد الإلكتروني هنا"
          label="البريد الإلكتروني"
          value={fields.email.value}
          onChange={handleChange}
          onBlur={handleBlur}
          error={fields.email.touched && Boolean(fields.email.error)}
          helperText={fields.email.touched && fields.email.error}
        />
      </Grid>
      <Grid item xs={12} sm={6}>
        <TextField
          fullWidth
          name="phone"
          placeholder="إدخال رقم الهاتف هنا"
          label="رقم الهاتف"
          value={fields.phone.value}
          onChange={handleChange}
          onBlur={handleBlur}
          error={fields.phone.touched && Boolean(fields.phone.error)}
          helperText={fields.phone.touched && fields.phone.error}
        />
      </Grid>
      <Grid item xs={12} sm={6}>
        <TextField
          fullWidth
          name="city"
          placeholder="إدخال المدينة هنا"
          label="المدينة"
          value={fields.city.value}
          onChange={handleChange}
          onBlur={handleBlur}
          error={fields.city.touched && Boolean(fields.city.error)}
          helperText={fields.city.touched && fields.city.error}
        />
      </Grid>
      <Grid item xs={12} sm={6}>
        <TextField
          fullWidth
          name="district"
          placeholder="إدخال الحي هنا"
          label="الحي"
          value={fields.district.value}
          onChange={handleChange}
          onBlur={handleBlur}
          error={fields.district.touched && Boolean(fields.district.error)}
          helperText={fields.district.touched && fields.district.error}
        />
      </Grid>
    </Grid>
  );
};

export default PersonalInfo;
