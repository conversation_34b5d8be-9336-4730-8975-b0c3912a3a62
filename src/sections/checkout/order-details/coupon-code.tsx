import { useState } from 'react';
import { <PERSON>, Typo<PERSON>, TextField, Button, Switch } from '@mui/material';
import { useTheme } from '@mui/material/styles';
import { useLocales } from 'src/locales';

const CouponCode = () => {
  const [hasCoupon, setHasCoupon] = useState(false);
  const [couponCode, setCouponCode] = useState('');
  const { t } = useLocales();
  const theme = useTheme();

  return (
    <Box sx={{ mt: 3 }}>
      <Box sx={{ display: 'flex', alignItems: 'center' }}>
        <Switch checked={hasCoupon} onChange={(e) => setHasCoupon(e.target.checked)} />
        <Typography>{t('coupon.have_discount_question')}</Typography>
      </Box>

      <Box sx={{ mt: 2, display: 'flex', gap: 1 }}>
        <TextField
          fullWidth
          size="small"
          value={couponCode}
          onChange={(e) => setCouponCode(e.target.value)}
          placeholder="TG3U1AY-1"
          disabled={!hasCoupon}
        />
        <Button
          variant="contained"
          sx={{
            borderRadius: '30px',
            bgcolor: theme.palette.primary.dark,
            '&:hover': { bgcolor: theme.palette.primary.main },
          }}
          disabled={!hasCoupon}
        >
          {t('common.activate')}
        </Button>
      </Box>

      {hasCoupon && couponCode && (
        <>
          <Typography sx={{ mt: 1, color: 'text.secondary' }}>
            {`${t('coupon.discount_label')} 10%`}
          </Typography>
          <Typography sx={{ mt: 1, color: 'text.secondary' }}>$80 $75</Typography>
        </>
      )}
    </Box>
  );
};

export default CouponCode;
