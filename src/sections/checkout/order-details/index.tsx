'use client';

import { Box, Typography } from '@mui/material';
import { useTheme } from '@mui/material/styles';
import { Icon } from '@iconify/react';
import { useCartStore } from 'src/store/user/useCartStore';
import { useLocales } from 'src/locales';
import CustomButton from 'src/layouts/_common/button-detail';
import OrderSummary from './order-summary';
import CouponCode from './coupon-code';
import { useOrderDetails } from './useOrderDetails';

const OrderDetails = () => {
  const { HandleOnBuyNow } = useOrderDetails();
  const { totalPrice } = useCartStore();
  const { t } = useLocales();
  const theme = useTheme();

  return (
    <Box>
      <Box sx={{ bgcolor: 'background.paper', borderRadius: '30px', p: '28px' }}>
        <Typography variant="h4" sx={{ mb: 4, fontWeight: 'bold' }}>
          {t('profile.orders.order_details')}
        </Typography>
        <OrderSummary totalPrice={totalPrice} isCheckout />
        <CouponCode />
        <Box sx={{ my: 3, display: 'flex', justifyContent: 'center' }}>
          <CustomButton
            sx={{
              display: 'flex',
              gap: '8px',
              height: '47px',
              maxWidth: '199px',
              width: '100%',
              bgcolor: theme.palette.secondary.main,
              '&:hover': {
                color: theme.palette.common.white,
                bgcolor: theme.palette.primary.main,
              },
            }}
            variant="contained"
            fullWidth
            onClick={HandleOnBuyNow}
          >
            <Icon
              color={theme.palette.common.white}
              icon="mdi-light:credit-card"
              width="22px"
              height="22px"
            />
            <Typography color={theme.palette.common.white}>
              {t('product_details.buy_now')}
            </Typography>
          </CustomButton>
        </Box>
      </Box>
    </Box>
  );
};
export default OrderDetails;
