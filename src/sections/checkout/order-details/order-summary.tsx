import { Box, Typography, Divider } from '@mui/material';
import { useLocales } from 'src/locales';
import { fCurrency } from 'src/utils/format-number';

const OrderSummary = ({ totalPrice, isCheckout }: { totalPrice: number; isCheckout?: boolean }) => {
  const { t } = useLocales();
  if (isCheckout) {
    return (
      <Box>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
          <Typography fontWeight="bold">{t('coupon.total')}</Typography>
          <Typography fontWeight="bold">{fCurrency(totalPrice)}</Typography>
        </Box>
        <Divider sx={{ my: 2 }} />
        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
          <Typography>{t('coupon.discount')}</Typography>
          <Typography>$80</Typography>
        </Box>
      </Box>
    );
  }
  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
        <Typography>{t('coupon.discount')}</Typography>
        <Typography>$80</Typography>
      </Box>

      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
        <Typography>{t('coupon.delivery')}</Typography>
        <Typography>$80</Typography>
      </Box>

      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
        <Typography>{t('coupon.tax')}</Typography>
        <Typography>$80</Typography>
      </Box>

      <Divider sx={{ my: 2 }} />

      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
        <Typography fontWeight="bold">{t('coupon.total')}</Typography>
        <Typography fontWeight="bold">{fCurrency(totalPrice)}</Typography>
      </Box>
    </Box>
  );
};

export default OrderSummary;
