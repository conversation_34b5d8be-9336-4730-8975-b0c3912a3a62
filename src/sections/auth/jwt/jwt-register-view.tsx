'use client';

import * as Yup from 'yup';
import { useForm } from 'react-hook-form';
import { useState } from 'react';
import { yupResolver } from '@hookform/resolvers/yup';
// @mui
import LoadingButton from '@mui/lab/LoadingButton';
import Link from '@mui/material/Link';
import Alert from '@mui/material/Alert';
import Stack from '@mui/material/Stack';
import IconButton from '@mui/material/IconButton';
import Typography from '@mui/material/Typography';
import InputAdornment from '@mui/material/InputAdornment';
// hooks
import { useBoolean } from 'src/hooks/use-boolean';
// routes
import { paths } from 'src/routes/paths';
import { RouterLink } from 'src/routes/components';
import { useSearchParams, useRouter } from 'src/routes/hooks';
// config
import { PATH_AFTER_LOGIN } from 'src/config-global';
// auth
import { useAuthContext } from 'src/auth/hooks';
// components
import Iconify from 'src/components/iconify';
import FormProvider, { RHFTextField } from 'src/components/hook-form';
import Logo from 'src/components/logo';
import { useLocales } from 'src/locales';

// ----------------------------------------------------------------------

export default function JwtRegisterView() {
  const { register } = useAuthContext();
  const router = useRouter();
  const [errorMsg, setErrorMsg] = useState('');
  const searchParams = useSearchParams();
  const returnTo = searchParams.get('returnTo');
  const password = useBoolean();
  const confirmPassword = useBoolean();
  const { t } = useLocales();

  const RegisterSchema = Yup.object().shape({
    firstName: Yup.string().max(15).required('First name is required'),
    lastName: Yup.string().max(15).required('Last name is required'),
    email: Yup.string().required('Email is required').email('Email must be a valid email address'),
    password: Yup.string().required('Password is required'),
  });

  const defaultValues = {
    firstName: '',
    lastName: '',
    email: '',
    password: '',
    confirmPassword: '',
  };

  const methods = useForm({
    resolver: yupResolver(RegisterSchema),
    defaultValues,
  });

  const {
    reset,
    handleSubmit,
    formState: { isSubmitting },
  } = methods;

  const onSubmit = handleSubmit(async (data) => {
    try {
      await register?.(data.firstName, data.lastName, data.email, data.password);

      router.push(returnTo || PATH_AFTER_LOGIN);
    } catch (error) {
      console.error(error);
      reset();
      setErrorMsg(typeof error === 'string' ? error : error.message);
    }
  });

  const renderHead = (
    <Stack spacing={2} sx={{ mb: 5 }} alignItems="center">
      <Logo />

      <Typography variant="h4">{t('auth.create_account')}</Typography>

      <Typography
        sx={{
          color: '#B6B6B6',
          fontSize: '20px',
        }}
        variant="body1"
      >
        {t('auth.create_account_description')}
      </Typography>
    </Stack>
  );

  // const renderTerms = (
  //   <Typography
  //     component="div"
  //     sx={{
  //       color: 'text.secondary',
  //       mt: 2.5,
  //       typography: 'caption',
  //       textAlign: 'center',
  //     }}
  //   >
  //     {'By signing up, I agree to '}
  //     <Link underline="always" color="text.primary">
  //       Terms of Service
  //     </Link>
  //     {' and '}
  //     <Link underline="always" color="text.primary">
  //       Privacy Policy
  //     </Link>
  //     .
  //   </Typography>
  // );
  const renderFooter = (
    <Stack direction="row" justifyContent="center" mt={3} gap={1}>
      <Typography
        sx={{
          color: '#B6B6B6',
        }}
        variant="body1"
      >
        {t('auth.have_account')}
      </Typography>
      <Link
        href={paths.auth.login}
        component={RouterLink}
        sx={{
          color: '#478CCF',
          fontWeight: '700',
        }}
        variant="body1"
      >
        {t('auth.login')}
      </Link>
    </Stack>
  );
  const renderForm = (
    <FormProvider methods={methods} onSubmit={onSubmit}>
      <Stack spacing={2.5}>
        {!!errorMsg && <Alert severity="error">{errorMsg}</Alert>}
        <RHFTextField
          name="firstName"
          placeholder={t('profile.user_data.firstNamePlaceholder')}
          sx={{
            borderRadius: 4,
            '& .MuiOutlinedInput-root': {
              borderRadius: 4, // To ensure the input itself has the borderRadius
              '&:hover .MuiOutlinedInput-notchedOutline': {
                borderColor: '#EFEFEF', // Prevent border color change on hover
              },
              '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                borderColor: '#EFEFEF', // Prevent border color change on hover
              },
            },
          }}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <Iconify icon="mage:user" />
              </InputAdornment>
            ),
          }}
        />
        <RHFTextField
          name="lastName"
          placeholder={t('profile.user_data.lastNamePlaceholder')}
          sx={{
            borderRadius: 4,
            '& .MuiOutlinedInput-root': {
              borderRadius: 4, // To ensure the input itself has the borderRadius
              '&:hover .MuiOutlinedInput-notchedOutline': {
                borderColor: '#EFEFEF', // Prevent border color change on hover
              },
              '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                borderColor: '#EFEFEF', // Prevent border color change on hover
              },
            },
          }}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <Iconify icon="mdi:user-card-details-outline" />
              </InputAdornment>
            ),
          }}
        />
        <RHFTextField
          name="email"
          placeholder={t('profile.user_data.emailPlaceholder')}
          sx={{
            borderRadius: 4,
            '& .MuiOutlinedInput-root': {
              borderRadius: 4, // To ensure the input itself has the borderRadius
              '&:hover .MuiOutlinedInput-notchedOutline': {
                borderColor: '#EFEFEF', // Prevent border color change on hover
              },
              '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                borderColor: '#EFEFEF', // Prevent border color change on hover
              },
            },
          }}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <Iconify icon="mage:email" />
              </InputAdornment>
            ),
          }}
        />

        <RHFTextField
          name="password"
          placeholder={t('profile.password.password')}
          type={password.value ? 'text' : 'password'}
          InputProps={{
            endAdornment: (
              <InputAdornment position="end">
                <IconButton onClick={password.onToggle} edge="end">
                  <Iconify icon={password.value ? 'solar:eye-bold' : 'solar:eye-closed-bold'} />
                </IconButton>
              </InputAdornment>
            ),
            startAdornment: (
              <InputAdornment position="start">
                <Iconify icon="akar-icons:key" />
              </InputAdornment>
            ),
          }}
          sx={{
            borderRadius: 4,
            '& .MuiOutlinedInput-root': {
              borderRadius: 4, // To ensure the input itself has the borderRadius
              '&:hover  .MuiOutlinedInput-notchedOutline': {
                borderColor: '#EFEFEF', // Prevent border color change on hover
              },
              '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                borderColor: '#EFEFEF', // Prevent border color change on hover
              },
            },
          }}
        />
        <RHFTextField
          name="confirmPassword"
          placeholder={t('profile.password.confirm_password')}
          type={confirmPassword.value ? 'text' : 'password'}
          InputProps={{
            endAdornment: (
              <InputAdornment position="end">
                <IconButton onClick={confirmPassword.onToggle} edge="end">
                  <Iconify
                    icon={confirmPassword.value ? 'solar:eye-bold' : 'solar:eye-closed-bold'}
                  />
                </IconButton>
              </InputAdornment>
            ),
            startAdornment: (
              <InputAdornment position="start">
                <Iconify icon="akar-icons:key" />
              </InputAdornment>
            ),
          }}
          sx={{
            borderRadius: 4,
            '& .MuiOutlinedInput-root': {
              borderRadius: 4, // To ensure the input itself has the borderRadius
              '&:hover  .MuiOutlinedInput-notchedOutline': {
                borderColor: '#EFEFEF', // Prevent border color change on hover
              },
              '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                borderColor: '#EFEFEF', // Prevent border color change on hover
              },
            },
          }}
        />

        <LoadingButton
          fullWidth
          color="inherit"
          size="large"
          sx={{
            background: '#478CCF',
            color: '#fff',
            '&:hover': {
              background: '#2e6aa7',
              color: '#fff',
            },
            borderRadius: 4,
          }}
          type="submit"
          variant="contained"
          loading={isSubmitting}
        >
          {t('auth.create_account')}
        </LoadingButton>
      </Stack>
    </FormProvider>
  );

  return (
    <>
      {renderHead}

      {renderForm}

      {renderFooter}
    </>
  );
}
