'use client';

import * as Yup from 'yup';
import { useForm } from 'react-hook-form';
import { useState } from 'react';
import { yupResolver } from '@hookform/resolvers/yup';
// @mui
import LoadingButton from '@mui/lab/LoadingButton';
import Link from '@mui/material/Link';
import Alert from '@mui/material/Alert';
import Stack from '@mui/material/Stack';
import IconButton from '@mui/material/IconButton';
import Typography from '@mui/material/Typography';
import InputAdornment from '@mui/material/InputAdornment';
// routes
import { paths } from 'src/routes/paths';
import { RouterLink } from 'src/routes/components';
import { useSearchParams, useRouter } from 'src/routes/hooks';
// config
import { PATH_AFTER_LOGIN } from 'src/config-global';
// hooks
import { useBoolean } from 'src/hooks/use-boolean';
// auth
import { useAuthContext } from 'src/auth/hooks';
// components
import Iconify from 'src/components/iconify';
import FormProvider, { RHFCheckbox, RHFTextField } from 'src/components/hook-form';
import Logo from 'src/components/logo';
import { useLocales } from 'src/locales';

// ----------------------------------------------------------------------

export default function JwtLoginView() {
  const { login } = useAuthContext();
  const router = useRouter();
  const [errorMsg, setErrorMsg] = useState('');
  const searchParams = useSearchParams();
  const returnTo = searchParams.get('returnTo');
  const password = useBoolean();
  const { t } = useLocales();

  const LoginSchema = Yup.object().shape({
    email: Yup.string().required('Email is required').email('Email must be a valid email address'),
    password: Yup.string().required('Password is required'),
  });

  const defaultValues = {
    email: '',
    password: '',
    remember: false,
  };

  const methods = useForm({
    resolver: yupResolver(LoginSchema),
    defaultValues,
  });

  const {
    reset,
    handleSubmit,
    formState: { isSubmitting },
  } = methods;

  const onSubmit = handleSubmit(async (data) => {
    try {
      await login?.(data.email, data.password);

      router.push(returnTo || PATH_AFTER_LOGIN);
    } catch (error) {
      console.error(error);
      reset();
      setErrorMsg(typeof error === 'string' ? error : t('auth.unexpected_login_error'));
    }
  });

  const renderHead = (
    <Stack spacing={2} sx={{ mb: 5 }} alignItems="center">
      <Logo />

      <Typography variant="h4">{t('auth.login')}</Typography>

      <Typography
        sx={{
          color: '#B6B6B6',
          fontSize: '20px',
        }}
        variant="body1"
      >
        {t('auth.welcome_back')}
      </Typography>
    </Stack>
  );

  const renderForm = (
    <Stack spacing={2.5}>
      {!!errorMsg && <Alert severity="error">{errorMsg}</Alert>}

      <RHFTextField
        name="email"
        placeholder={t('profile.user_data.email')}
        sx={{
          borderRadius: 4,
          '& .MuiOutlinedInput-root': {
            borderRadius: 4, // To ensure the input itself has the borderRadius
            '&:hover .MuiOutlinedInput-notchedOutline': {
              borderColor: '#EFEFEF', // Prevent border color change on hover
            },
            '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
              borderColor: '#EFEFEF', // Prevent border color change on hover
            },
          },
        }}
        InputProps={{
          startAdornment: (
            <InputAdornment position="start">
              <Iconify icon="mage:email" />
            </InputAdornment>
          ),
        }}
      />

      <RHFTextField
        name="password"
        placeholder={t('profile.password.password')}
        type={password.value ? 'text' : 'password'}
        InputProps={{
          endAdornment: (
            <InputAdornment position="end">
              <IconButton onClick={password.onToggle} edge="end">
                <Iconify icon={password.value ? 'solar:eye-bold' : 'solar:eye-closed-bold'} />
              </IconButton>
            </InputAdornment>
          ),
          startAdornment: (
            <InputAdornment position="start">
              <Iconify icon="akar-icons:key" />
            </InputAdornment>
          ),
        }}
        sx={{
          borderRadius: 4,
          '& .MuiOutlinedInput-root': {
            borderRadius: 4, // To ensure the input itself has the borderRadius
            '&:hover  .MuiOutlinedInput-notchedOutline': {
              borderColor: '#EFEFEF', // Prevent border color change on hover
            },
            '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
              borderColor: '#EFEFEF', // Prevent border color change on hover
            },
          },
        }}
      />
      <Stack direction="row" justifyContent="space-between">
        <RHFCheckbox
          name="remember"
          label={t('auth.remember_me')}
          sx={{
            color: '#B6B6B6',
            fontSize: '20px',
          }}
        />
        <Link variant="body1" color="inherit" sx={{ alignSelf: 'flex-end', color: '#B6B6B6' }}>
          {t('auth.forgot_password_question')}
        </Link>
      </Stack>

      <LoadingButton
        fullWidth
        color="inherit"
        size="large"
        sx={{
          background: '#478CCF',
          color: '#fff',
          '&:hover': {
            background: '#2e6aa7',
            color: '#fff',
          },
          borderRadius: 4,
        }}
        type="submit"
        variant="contained"
        loading={isSubmitting}
      >
        {t('auth.login')}
      </LoadingButton>
    </Stack>
  );

  const renderFooter = (
    <Stack direction="row" justifyContent="center" mt={3} gap={1}>
      <Typography
        sx={{
          color: '#B6B6B6',
        }}
        variant="body1"
      >
        {t('auth.dont_have_account')}
      </Typography>
      <Link
        href={paths.auth.register}
        component={RouterLink}
        sx={{
          color: '#478CCF',
          fontWeight: '700',
        }}
        variant="body1"
      >
        {t('auth.create_account')}
      </Link>
    </Stack>
  );

  return (
    <FormProvider methods={methods} onSubmit={onSubmit}>
      {renderHead}
      {renderForm}
      {renderFooter}
    </FormProvider>
  );
}
