import { Box } from '@mui/material';
import InputTextField from 'src/components/text-field';
import CustomButton from 'src/components/button';
import { useLocales } from 'src/locales';
import { usePasswordChange } from './usePasswordChange';

const PasswordChange = () => {
  const { handleSubmit, handleChange, formData, errors } = usePasswordChange();
  const { t } = useLocales();
  return (
    <Box
      sx={{
        width: '100%',
        display: 'flex',
        flexDirection: 'column',
        maxWidth: '965px',
      }}
      component="form"
      onSubmit={handleSubmit}
    >
      <Box sx={{ mb: 4 }}>
        <InputTextField
          label={t('profile.password.current_password')}
          placeholder={t('profile.password.current_password')}
          maxWidth="460px"
          maxHeight="50px"
          name="old_password"
          onChange={handleChange}
          value={formData.old_password}
          error={!!errors.old_password}
          helperText={errors.old_password}
        />
      </Box>
      <Box
        sx={{
          display: 'flex',
          flexFlow: 'row wrap',
          justifyContent: 'space-between',
          gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
          gap: 3,
          mb: 4,
        }}
      >
        <InputTextField
          label={t('profile.password.new_password')}
          placeholder={t('profile.password.new_password')}
          maxWidth="460px"
          maxHeight="50px"
          name="password"
          onChange={handleChange}
          value={formData.password}
          error={!!errors.password}
          helperText={errors.password}
        />

        <InputTextField
          label={t('profile.password.confirm_password')}
          placeholder={t('profile.password.confirm_password')}
          maxWidth="460px"
          maxHeight="50px"
          name="password_confirmation"
          onChange={handleChange}
          value={formData.password_confirmation}
          error={!!errors.password_confirmation}
          helperText={errors.password_confirmation}
        />
      </Box>
      <CustomButton
        isPrimary
        sx={{ alignSelf: 'end', maxHeight: '42px', maxWidth: '199px' }}
        type="submit"
        handleOnClick={handleSubmit}
      >
        {t('common.submit')}
      </CustomButton>
    </Box>
  );
};
export default PasswordChange;
