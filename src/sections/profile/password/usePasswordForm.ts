import { useForm } from 'react-hook-form';
import * as yup from 'yup';
import { yupResolver } from '@hookform/resolvers/yup';
import { TFunction } from 'i18next';
import { useLocales } from 'src/locales';

const schema = (t: TFunction) =>
  yup.object({
    currentPassword: yup.string().required(t('profile.password.current_password_required')),
    newPassword: yup
      .string()
      .min(8, t('profile.password.new_password_min_length'))
      .required(t('profile.password.new_password_required')),
    confirmPassword: yup
      .string()
      .oneOf([yup.ref('newPassword')], t('profile.password.password_not_match'))
      .required(t('profile.password.confirm_password_required')),
  });

export type PasswordFormData = yup.InferType<ReturnType<typeof schema>>;

export const usePasswordForm = () => {
  const { t } = useLocales();
  const {
    register,
    handleSubmit,
    formState: { errors, dirtyFields },
  } = useForm<PasswordFormData>({
    resolver: yupResolver(schema(t)),
  });

  const onSubmit = (data: PasswordFormData) => {
    console.log('Password change submitted:', data);
  };

  return {
    register,
    handleSubmit: handleSubmit(onSubmit),
    errors,
    dirtyFields,
  };
};
