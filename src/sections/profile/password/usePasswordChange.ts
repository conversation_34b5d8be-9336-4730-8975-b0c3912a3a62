import { useState } from 'react';
import { useUpdateUserPassword } from 'src/api/profile';
import { IPasswordData } from '../types';

export const usePasswordChange = () => {
  const { handlePost } = useUpdateUserPassword();

  const [formData, setFormData] = useState<IPasswordData>({
    old_password: '',
    password: '',
    password_confirmation: '',
  });

  const [errors, setErrors] = useState<Partial<IPasswordData>>({});
  const [isLoading, setIsLoading] = useState(false);
  const validateForm = () => {
    const newErrors: Partial<IPasswordData> = {};

    if (!formData.old_password) {
      newErrors.old_password = 'كلمة المرور الحالية مطلوبة';
    }

    if (!formData.password) {
      newErrors.password = 'كلمة المرور الجديدة مطلوبة';
    } else if (formData.password.length < 8) {
      newErrors.password = 'كلمة المرور يجب أن تكون 8 أحرف على الأقل';
    }

    if (!formData.password_confirmation) {
      newErrors.password_confirmation = 'تأكيد كلمة المرور مطلوب';
    } else if (formData.password_confirmation !== formData.password) {
      newErrors.password_confirmation = 'كلمة المرور غير متطابقة';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    if (validateForm()) {
      // Handle password change
      const { success } = await handlePost(formData);
      if (success) {
        // TODO: toast.success('تم تغيير كلمة المرور بنجاح');
        setIsLoading(false);
      } else {
        // TODO: toast.error('حدث خطأ ما');
        setErrors({
          ...errors,
          old_password: 'كلمة المرور الحالية غير متطابقة',
        });
        setIsLoading(false);
      }
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
    if (errors[name as keyof IPasswordData]) {
      setErrors((prev) => ({ ...prev, [name]: '' }));
    }
  };

  return {
    formData,
    errors,
    handleChange,
    handleSubmit,
    isLoading,
  };
};
