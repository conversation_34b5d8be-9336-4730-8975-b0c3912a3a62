import { useEffect } from 'react';
import { Box, Skeleton } from '@mui/material';
import ImageUpload from 'src/components/image-uploader';
import InputTextField from 'src/components/text-field';
import CustomButton from 'src/components/button';
import { useGetPersonalInfo } from 'src/api/profile';
import { useLocales } from 'src/locales';
import { useProfileForm } from './useProfileData';

const ProfileData = () => {
  const { personalInfo, personalInfoLoading } = useGetPersonalInfo();
  const { handleSubmit, handleImageChange, watch, register, reset } = useProfileForm({
    id: personalInfo?.id || 0,
    first_name: personalInfo?.first_name || '',
    last_name: personalInfo?.last_name || '',
    email: personalInfo?.email || '',
    gender: personalInfo?.gender || '',
    birth_date: personalInfo?.birth_date || '',
    role: personalInfo?.role || '',
    email_verified_at: personalInfo?.email_verified_at || '',
    phone_verified_at: personalInfo?.phone_verified_at || '',
    last_login_at: personalInfo?.last_login_at || '',
    status: personalInfo?.status || '',
    is_verify: personalInfo?.is_verify || false,
    created_at: personalInfo?.created_at || '',
    updated_at: personalInfo?.updated_at || '',
    phone: personalInfo?.phone || '',
    city: '',
    district: '',
    image:
      personalInfo?.image ||
      'https://www.pngitem.com/pimgs/m/130-1300305_user-female-alt-icon-default-user-image-png.png',
  });
  const { t } = useLocales();
  useEffect(() => {
    if (personalInfo) {
      reset({
        firstName: personalInfo.first_name,
        lastName: personalInfo.last_name,
        email: personalInfo.email,
        phone: personalInfo.phone || '',
        gender: personalInfo.gender,
        birth_date: personalInfo.birth_date,
        image:
          personalInfo.image ||
          'https://www.pngitem.com/pimgs/m/130-1300305_user-female-alt-icon-default-user-image-png.png',
        city: '',
        district: '',
      });
    }
  }, [personalInfo, reset]);

  const values = watch();

  if (personalInfoLoading) {
    return (
      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 4 }}>
        {/* Image Skeleton */}
        <Box sx={{ flex: '0 0 auto' }}>
          <Skeleton variant="circular" width={200} height={200} />
        </Box>

        {/* Form Fields Skeleton */}
        <Box
          sx={{ display: 'flex', flexFlow: 'row wrap', width: '100%', maxWidth: '970px', gap: 3 }}
        >
          {[...Array(6)].map((_, index) => (
            <Skeleton
              key={index}
              variant="rectangular"
              width={460}
              height={50}
              sx={{ borderRadius: 1 }}
            />
          ))}
          <Skeleton variant="rectangular" width={199} height={42} sx={{ borderRadius: 1 }} />
        </Box>
      </Box>
    );
  }
  return (
    <Box
      component="form"
      onSubmit={handleSubmit}
      sx={{
        display: 'flex',
        flexWrap: 'wrap',
        gap: 4,
      }}
    >
      <Box sx={{ flex: '0 0 auto' }}>
        <ImageUpload currentImage={values.image} onImageChange={handleImageChange} />
      </Box>

      <Box
        sx={{
          display: 'flex',
          flexFlow: 'row wrap',
          justifyContent: 'space-between',
          width: '100%',
          maxWidth: '970px',
          gap: 3,
        }}
      >
        <InputTextField
          label={t('profile.user_data.firstName')}
          maxHeight="50px"
          maxWidth="460px"
          value={values.firstName}
          placeholder={t('profile.user_data.firstNamePlaceholder')}
          {...register('firstName')}
        />
        <InputTextField
          label={t('profile.user_data.lastName')}
          maxHeight="50px"
          maxWidth="460px"
          value={values.lastName}
          placeholder={t('profile.user_data.lastNamePlaceholder')}
          {...register('lastName')}
        />
        <InputTextField
          label={t('profile.user_data.email')}
          maxHeight="50px"
          maxWidth="460px"
          value={values.email}
          placeholder={t('profile.user_data.emailPlaceholder')}
          {...register('email')}
        />
        <InputTextField
          label={t('profile.user_data.phone')}
          maxHeight="50px"
          maxWidth="460px"
          value={values.phone}
          placeholder={t('profile.user_data.phonePlaceholder')}
          {...register('phone')}
        />
        <InputTextField
          label={t('profile.user_data.city')}
          maxHeight="50px"
          maxWidth="460px"
          value={values.city}
          placeholder={t('profile.user_data.cityPlaceholder')}
          {...register('city')}
        />
        <InputTextField
          label={t('profile.user_data.district')}
          maxHeight="50px"
          maxWidth="460px"
          value={values.district}
          placeholder={t('profile.user_data.districtPlaceholder')}
          {...register('district')}
        />
        <Box sx={{ width: '100%' }}>
          <CustomButton
            handleOnClick={() => {}}
            sx={{
              width: '100%',
              justifySelf: 'end',
              display: 'flex',
              maxHeight: '42px',
              maxWidth: '199px',
            }}
          >
            {t('common.submit')}
          </CustomButton>
        </Box>
      </Box>
    </Box>
  );
};

export default ProfileData;
