import { TableRow, TableCell, Button, Typography } from '@mui/material';
import { useLocales } from 'src/locales';
import { IOrderData } from '../types';

interface OrderItemProps {
  order: IOrderData;
  onEdit: (id: string) => void;
  onCancel: (id: string) => void;
}

const OrderItem = ({ order, onEdit, onCancel }: OrderItemProps) => {
  const { t } = useLocales();
  const isCompleted = order.status === t('profile.orders.order_completed');

  return (
    <TableRow>
      <TableCell>{order.orderId}</TableCell>
      <TableCell>{order.date}</TableCell>
      <TableCell>{order.paymentMethod}</TableCell>
      <TableCell>{order.status}</TableCell>
      <TableCell>${order.total}</TableCell>
      <TableCell>
        <Button variant="contained" color="primary" onClick={() => onEdit(order.orderId)}>
          {isCompleted ? t('profile.orders.order_details') : t('profile.orders.edit_order')}
        </Button>
        {!isCompleted && (
          <Button
            variant="contained"
            color="error"
            onClick={() => onCancel(order.orderId)}
            sx={{ ml: 1 }}
          >
            {t('profile.orders.cancel_order')}
          </Button>
        )}
        {isCompleted && (
          <Typography color="success.main" sx={{ mt: 1 }}>
            {t('profile.orders.order_confirmed')}
          </Typography>
        )}
      </TableCell>
    </TableRow>
  );
};

export default OrderItem;
