import {
  Table,
  TableHead,
  TableBody,
  TableRow,
  TableCell,
  Box,
  Typography,
  Skeleton,
  Stack,
} from '@mui/material';
import { useTheme } from '@mui/material/styles';
import { useGetPersonalOrders } from 'src/api/profile';
import CustomButton from 'src/components/button';
import { useLocales } from 'src/locales';

const OrdersList = () => {
  const theme = useTheme();
  const { orders = [], paginationInfo = {}, ordersEmpty, ordersLoading } = useGetPersonalOrders();

  const { t } = useLocales();
  const tableHeader = [
    t('profile.orders.order_number'),
    t('profile.orders.date'),
    t('profile.orders.payment_method'),
    t('profile.orders.order_status'),
    t('profile.orders.total'),
    t('profile.orders.actions'),
  ];
  if (ordersLoading) {
    return (
      <Stack direction="column">
        <Skeleton variant="rectangular" width="100%" height={50} sx={{ my: 1 }} />
        <Skeleton variant="rectangular" width="100%" height={50} sx={{ my: 1 }} />
        <Skeleton variant="rectangular" width="100%" height={50} sx={{ my: 1 }} />
        <Skeleton variant="rectangular" width="100%" height={50} sx={{ my: 1 }} />
      </Stack>
    );
  }
  if (ordersEmpty) {
    return (
      <Box sx={{ display: 'flex', alignContent: 'center', justifyContent: 'center' }}>
        <Typography variant="h4">{t('profile.orders.empty')}</Typography>
      </Box>
    );
  }
  return (
    <Table>
      <TableHead sx={{ direction: 'rtl' }}>
        <TableRow sx={{ borderBottom: `1px solid ${theme.palette.divider}` }}>
          {tableHeader.map((header) => (
            <TableCell
              sx={{
                backgroundColor: theme.palette.background.paper,
                color: theme.palette.text.primary,
              }}
            >
              {header}
            </TableCell>
          ))}
        </TableRow>
      </TableHead>
      <TableBody>
        {orders.map((order) => (
          <TableRow key={order.id}>
            <TableCell>{order.id}</TableCell>
            <TableCell>{order.created_at}</TableCell>
            <TableCell>{order.payment_method}</TableCell>
            <TableCell>{order.status.value}</TableCell>
            <TableCell>${order.total}</TableCell>
            <TableCell>
              <Box sx={{ display: 'flex', gap: '20px', alignContent: 'center' }}>
                <CustomButton
                  handleOnClick={() => {}}
                  sx={{ maxHeight: '42px', maxWidth: '185px' }}
                >
                  {t('profile.orders.edit_order')}
                </CustomButton>
                {order.confirmed ? (
                  <Typography
                    sx={{
                      width: '100%',
                      maxHeight: '42px',
                      maxWidth: '185px',
                      textAlign: 'center',
                      fontSize: '20px',
                    }}
                  >
                    {t('profile.orders.order_confirmed')}
                  </Typography>
                ) : (
                  <CustomButton
                    isPrimary
                    handleOnClick={() => {}}
                    sx={{
                      maxHeight: '42px',
                      maxWidth: '185px',
                      bgcolor: theme.palette.primary.dark,
                      '&:hover': {
                        bgcolor: theme.palette.primary.main,
                      },
                    }}
                  >
                    {t('profile.orders.cancel_order')}
                  </CustomButton>
                )}
              </Box>
            </TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  );
};

export default OrdersList;
