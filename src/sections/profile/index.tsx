'use client';

import { useState } from 'react';
import { Container, Box } from '@mui/material';
import Tabs from 'src/components/tabs';
import { useLocales } from 'src/locales';
import ProfileData from './profile-data';
import OrdersList from './orders';
import FavoritesList from './favourite';
import PasswordChange from './password/password-change';

const ProfilePage = () => {
  const [currentTab, setCurrentTab] = useState(0);
  const [loadedTabs, setLoadedTabs] = useState<Record<number, boolean>>({ 0: true }); // Track loaded tabs

  const handleTabChange = (_: React.SyntheticEvent, newValue: number) => {
    setCurrentTab(newValue);
    setLoadedTabs((prev) => ({ ...prev, [newValue]: true })); // Mark tab as loaded
  };

  const { t } = useLocales();

  const tabs = [
    t('profile.profile_data'),
    t('profile.my_orders'),
    t('profile.my_favourite'),
    t('profile.change_password'),
  ];

  return (
    <Container sx={{ maxWidth: '1640px !important', py: 0, mt: '23px' }}>
      <Box sx={{ overflow: 'auto' }}>
        <Tabs value={currentTab} labels={tabs} onChange={handleTabChange} />

        <Box hidden={currentTab !== 0}>
          <ProfileData />
        </Box>
        {loadedTabs[1] && (
          <Box hidden={currentTab !== 1}>
            <OrdersList />
          </Box>
        )}
        {loadedTabs[2] && (
          <Box hidden={currentTab !== 2}>
            <FavoritesList />
          </Box>
        )}
        {loadedTabs[3] && (
          <Box hidden={currentTab !== 3}>
            <PasswordChange />
          </Box>
        )}
      </Box>
    </Container>
  );
};

export default ProfilePage;
