import { useForm } from 'react-hook-form';
import * as yup from 'yup';
import { yupResolver } from '@hookform/resolvers/yup';
import { IUser } from 'src/auth/types';
// import { useAuthStore } from '@/store/useAuthStore';

const schema = yup.object({
  firstName: yup.string().required('الاسم الأول مطلوب'),
  lastName: yup.string().required('الاسم الثاني مطلوب'),
  email: yup.string().email('البريد الإلكتروني غير صحيح').required('البريد الإلكتروني مطلوب'),
  phone: yup
    .string()
    .matches(/^[0-9]+$/, 'رقم الهاتف غير صحيح')
    .required('رقم الهاتف مطلوب'),
  city: yup.string().required('المدينة مطلوبة'),
  district: yup.string().required('المحافظة مطلوبة'),
  image: yup
    .string()
    .default(
      'https://www.pngitem.com/pimgs/m/130-1300305_user-female-alt-icon-default-user-image-png.png'
    ),
  gender: yup.string().required('الجنس مطلوب'),
  birth_date: yup.string().required('تاريخ الميلاد مطلوب'),
  role: yup.string().required('الدور مطلوب'),
  email_verified_at: yup.string().required('تاريخ التوثيق مطلوب'),
  phone_verified_at: yup.string().required('تاريخ التوثيق مطلوب'),
});

export type ProfileFormData = yup.InferType<typeof schema>;

export const useProfileForm = (user: IUser | undefined) => {
  const {
    register,
    handleSubmit,
    watch,
    formState: { errors, dirtyFields },
    setValue,
    reset,
  } = useForm<ProfileFormData>({
    resolver: yupResolver(schema),
    defaultValues: {
      firstName: user?.first_name ?? '',
      lastName: user?.last_name ?? '',
      email: user?.email ?? '',
      phone: user?.phone ?? '',
      city: '',
      district: '',
      image:
        user?.image ??
        'https://www.pngitem.com/pimgs/m/130-1300305_user-female-alt-icon-default-user-image-png.png',
      gender: user?.gender ?? '',
      birth_date: user?.birth_date ?? '',
      role: user?.role ?? '',
      email_verified_at: user?.email_verified_at ?? '',
      phone_verified_at: user?.phone_verified_at ?? '',
    },
  });

  const onSubmit = (data: ProfileFormData) => {
    Object.keys(dirtyFields).reduce<Partial<ProfileFormData>>((acc, key) => {
      if (key in data) {
        acc[key as keyof ProfileFormData] = data[key as keyof ProfileFormData];
      }
      return acc;
    }, {});

    // Now you can send only the changed fields to your API
    // updateProfile(changedData);
  };

  const handleImageChange = (image: string) => {
    setValue('image', image);
  };

  return {
    register,
    handleSubmit: handleSubmit(onSubmit),
    errors,
    dirtyFields,
    handleImageChange,
    watch,
    reset,
    formState: { errors, dirtyFields },
  };
};
