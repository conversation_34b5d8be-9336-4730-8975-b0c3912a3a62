import {
  <PERSON>Row,
  <PERSON><PERSON>ell,
  Box,
  IconButton,
  Typo<PERSON>,
  Card,
  CardContent,
  Divider,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
} from '@mui/material';
import { Icon } from '@iconify/react';
import { useRouter } from 'next/navigation';
import { useTheme } from '@mui/material/styles';
import { useState } from 'react';
import CustomButton from 'src/components/button';
import Image from 'src/components/image';
import QuantitySelector from 'src/layouts/product-details/quantity-selector';
import { IProductItem } from 'src/types/product';
import { useLocales } from 'src/locales';
import { fCurrency } from 'src/utils/format-number';

interface FavoriteItemProps {
  item: IProductItem;
  onRemove: (id: number) => void;
  onAddToCart: (id: number, quantity: number) => void;
  isMobile?: boolean;
}

const FavoriteItem = ({ item, onRemove, onAddToCart, isMobile = false }: FavoriteItemProps) => {
  const theme = useTheme();
  const router = useRouter();
  const { t } = useLocales();
  const [quantity, setQuantity] = useState(item.quantity || 1);
  const [openConfirm, setOpenConfirm] = useState(false);

  const handleDeleteClick = () => {
    setOpenConfirm(true);
  };

  const handleConfirmDelete = () => {
    onRemove(item.id);
    setOpenConfirm(false);
  };

  const handleCancelDelete = () => {
    setOpenConfirm(false);
  };

  // Mobile view
  if (isMobile) {
    return (
      <>
        <Card variant="outlined" sx={{ mb: 2 }}>
          <CardContent>
            <Box display="flex" alignItems="center" gap={2}>
              <Image
                src={item.image}
                alt={item.name}
                width="72px"
                height="72px"
                borderRadius="10px"
                border="1px solid #E8E8E8"
                style={{ objectFit: 'cover' }}
              />
              <Typography
                sx={{
                  fontSize: '18px',
                  fontWeight: 'bold',
                  whiteSpace: 'nowrap',
                  textOverflow: 'ellipsis',
                  overflow: 'hidden',
                }}
              >
                {item.name}
              </Typography>
            </Box>

            <Box mt={2} display="flex" alignItems="center" gap={1}>
              <Icon icon="mdi:tag-outline" width={24} height={24} />
              <Typography fontWeight="bold" color={theme.palette.primary.main}>
                {fCurrency(item.price_for_selling)}
              </Typography>
            </Box>

            <Box mt={2}>
              <QuantitySelector
                onIncrement={() => setQuantity((prev) => Math.min(prev + 1, 99))}
                onDecrement={() => setQuantity((prev) => Math.max(prev - 1, 1))}
              >
                {quantity}
              </QuantitySelector>
            </Box>

            <Divider sx={{ my: 2 }} />

            <Box display="flex" gap={2} flexWrap="wrap">
              <CustomButton
                isPrimary
                handleOnClick={() => router.push(`/product/${item.id}`)}
                sx={{
                  bgcolor: theme.palette.primary.dark,
                  '&:hover': {
                    bgcolor: theme.palette.primary.main,
                  },
                }}
              >
                {t('common.view_details')}
              </CustomButton>
              <CustomButton handleOnClick={() => onAddToCart(item.id, quantity)}>
                {t('common.add_to_cart')}
              </CustomButton>
              <CustomButton
                isPrimary
                handleOnClick={handleDeleteClick}
                sx={{
                  bgcolor: theme.palette.error.dark,
                  '&:hover': {
                    bgcolor: theme.palette.primary.main,
                  },
                }}
              >
                {t('common.delete')}
              </CustomButton>
            </Box>
          </CardContent>
        </Card>

        {/* Confirmation Dialog */}
        <Dialog open={openConfirm} onClose={handleCancelDelete}>
          <DialogTitle>{t('common.confirm_delete')}</DialogTitle>
          <DialogContent>
            <Typography>{t('common.are_you_sure')}</Typography>
          </DialogContent>
          <DialogActions>
            <Button onClick={handleCancelDelete}>{t('common.cancel')}</Button>
            <Button onClick={handleConfirmDelete} color="error">
              {t('common.delete')}
            </Button>
          </DialogActions>
        </Dialog>
      </>
    );
  }

  // Desktop view
  return (
    <>
      <TableRow>
        <TableCell>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Image
              src={item.image}
              alt={item.name}
              width="72px"
              height="72px"
              borderRadius="10px"
              border="1px solid #E8E8E8"
              style={{ objectFit: 'cover' }}
            />
            <Typography
              sx={{
                fontSize: '20px',
                fontWeight: 'bold',
                width: '100%',
                maxWidth: '200px',
                whiteSpace: 'nowrap',
                textOverflow: 'ellipsis',
                overflow: 'hidden',
              }}
            >
              {item.name}
            </Typography>
          </Box>
        </TableCell>
        <TableCell>
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              gap: 1,
              color: theme.palette.primary.main,
            }}
          >
            <Icon icon="mdi:tag-outline" width={24} height={24} />
            <Typography sx={{ fontWeight: 'bold' }}>{fCurrency(item.price_for_selling)}</Typography>
          </Box>
        </TableCell>
        <TableCell>
          <QuantitySelector
            onIncrement={() => setQuantity((prev) => Math.min(prev + 1, 99))}
            onDecrement={() => setQuantity((prev) => Math.max(prev - 1, 1))}
          >
            {quantity}
          </QuantitySelector>
        </TableCell>
        <TableCell>
          <Typography sx={{ fontWeight: 'bold', color: theme.palette.text.primary }}>
            {fCurrency(item.price_for_selling * quantity)}
          </Typography>
        </TableCell>
        <TableCell>
          <Box sx={{ display: 'flex', gap: '20px', alignItems: 'center' }}>
            <CustomButton
              isPrimary
              handleOnClick={() => router.push(`/product/${item.id}`)}
              sx={{
                maxHeight: '42px',
                maxWidth: '185px',
                bgcolor: theme.palette.primary.dark,
                '&:hover': {
                  bgcolor: theme.palette.primary.main,
                },
              }}
            >
              {t('common.view_details')}
            </CustomButton>
            <CustomButton
              handleOnClick={() => onAddToCart(item.id, quantity)}
              sx={{ maxHeight: '42px', maxWidth: '185px' }}
            >
              {t('common.add_to_cart')}
            </CustomButton>
            <IconButton
              onClick={handleDeleteClick}
              sx={{
                border: 1,
                borderColor: theme.palette.primary.main,
                borderRadius: '50%',
                mx: '16px',
                color: theme.palette.primary.main,
              }}
            >
              <Icon icon="mdi:close" width="24px" height="24px" />
            </IconButton>
          </Box>
        </TableCell>
      </TableRow>

      {/* Confirmation Dialog */}
      <Dialog open={openConfirm} onClose={handleCancelDelete}>
        <DialogTitle>{t('common.confirm')}</DialogTitle>
        <DialogContent>
          <Typography>{t('common.delete_confirmation')}</Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCancelDelete}>{t('common.cancel')}</Button>
          <Button onClick={handleConfirmDelete} color="error">
            {t('common.delete')}
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default FavoriteItem;
