import { useSnackbar } from 'notistack';
import { useCartAPIRequest } from 'src/api/cart';
import { useToggleFavorite } from 'src/api/product';
import { useLocales } from 'src/locales';
import { useCartStore } from 'src/store/user/useCartStore';

export function useFavouriteUser() {
  const { handleToggleFavorite } = useToggleFavorite();
  const { addBook } = useCartStore();
  const { handleAddToCart } = useCartAPIRequest();
  const { enqueueSnackbar } = useSnackbar();
  const { t } = useLocales();

  const removeFromFavorites = async (id: number, setFavoritesList: (favorites: any) => void) => {
    try {
      const response = await handleToggleFavorite(id);
      if (response.success) {
        enqueueSnackbar(t('profile.favourite.item_removed'), {
          variant: 'success',
        });
        setFavoritesList((prev: any) => prev.filter((item: any) => item.id !== id));
      } else {
        enqueueSnackbar(t('profile.favourite.error_removing_item'), {
          variant: 'error',
        });
      }
    } catch (error) {
      console.error(error);
      enqueueSnackbar(t('profile.favourite.unexpected_error_removing_item'), {
        variant: 'error',
      });
    }
  };

  const addToCart = async (id: number, quantity: number) => {
    try {
      const response = await handleAddToCart(id, quantity);
      if (response.success) {
        addBook(id, quantity);
        enqueueSnackbar(t('cart.item_added'), {
          variant: 'success',
        });
      } else {
        enqueueSnackbar(t('cart.error_adding_item'), {
          variant: 'error',
        });
      }
    } catch (error) {
      enqueueSnackbar(t('cart.error_adding_item'), {
        variant: 'error',
      });
      console.error(error);
    }
  };

  return { removeFromFavorites, addToCart };
}
