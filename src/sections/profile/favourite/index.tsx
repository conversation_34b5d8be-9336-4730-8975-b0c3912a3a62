import {
  Table,
  TableHead,
  TableBody,
  TableRow,
  TableCell,
  Typography,
  Skeleton,
  Stack,
  useMediaQuery,
} from '@mui/material';
import { useTheme } from '@mui/material/styles';
import { useEffect, useState } from 'react';
import { Box } from '@mui/system';
import { useGetPersonalFavorites } from 'src/api/profile';
import { useLocales } from 'src/locales';
import FavoriteItem from './favourite-item';
import { useFavouriteUser } from './useFavouriteUser';

const FavoritesList = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const { favorites = [], favoritesEmpty, favoritesLoading } = useGetPersonalFavorites();
  const [favoritesList, setFavoritesList] = useState(favorites);

  useEffect(() => {
    setFavoritesList(favorites);
  }, [favorites]);

  const { removeFromFavorites, addToCart } = useFavouriteUser();
  const { t } = useLocales();

  const tableHeader = [
    t('profile.favourite.product'),
    t('profile.favourite.price'),
    t('profile.favourite.quantity'),
    t('profile.favourite.total'),
    t('profile.favourite.actions'),
  ];

  if (favoritesLoading) {
    return (
      <Stack direction="column">
        <Skeleton variant="rectangular" width="100%" height={50} sx={{ my: 1 }} />
        <Skeleton variant="rectangular" width="100%" height={50} sx={{ my: 1 }} />
        <Skeleton variant="rectangular" width="100%" height={50} sx={{ my: 1 }} />
        <Skeleton variant="rectangular" width="100%" height={50} sx={{ my: 1 }} />
      </Stack>
    );
  }

  if (favoritesEmpty) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignContent: 'center' }}>
        <Typography variant="h4">{t('profile.favourite.empty')}</Typography>
      </Box>
    );
  }

  return (
    <Box>
      {isMobile ? (
        favoritesList.map((item) => (
          <FavoriteItem
            key={item.id}
            item={item}
            onAddToCart={addToCart}
            onRemove={() => removeFromFavorites(item.id, setFavoritesList)}
            isMobile
          />
        ))
      ) : (
        <Table>
          <TableHead>
            <TableRow sx={{ borderBottom: `1px solid ${theme.palette.divider}` }}>
              {tableHeader.map((header) => (
                <TableCell
                  key={header}
                  sx={{
                    backgroundColor: theme.palette.background.paper,
                    color: theme.palette.text.primary,
                    textAlign: 'center',
                  }}
                >
                  {header}
                </TableCell>
              ))}
            </TableRow>
          </TableHead>
          <TableBody>
            {favoritesList.map((item) => (
              <FavoriteItem
                key={item.id}
                item={item}
                onAddToCart={addToCart}
                onRemove={() => removeFromFavorites(item.id, setFavoritesList)}
              />
            ))}
          </TableBody>
        </Table>
      )}
    </Box>
  );
};

export default FavoritesList;
