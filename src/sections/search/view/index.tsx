'use client';

import { Container } from '@mui/material';
import React from 'react';
import CustomBreadcrumbs from 'src/components/custom-breadcrumbs';
import { useLocales } from 'src/locales';
// import { paths } from 'src/routes/paths'

export default function SearchView() {
  const { t } = useLocales();
  return (
    <Container
      maxWidth="lg"
      sx={{
        mb: 15,
      }}
    >
      <CustomBreadcrumbs
        heading="List"
        links={[{ name: t('common.home'), href: '/' }, { name: t('common.search') }]}
        sx={{ mb: { xs: 3, md: 5 } }}
      />
    </Container>
  );
}
