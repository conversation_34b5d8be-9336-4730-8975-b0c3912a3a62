// components/HeaderRow.tsx
import { Box, Checkbox, Typography } from '@mui/material';
import { Theme } from '@mui/material/styles';
import { useLocales } from 'src/locales';

export default function HeaderRow({
  allSelected,
  onToggleAll,
  theme,
}: {
  allSelected: boolean;
  onToggleAll: () => void;
  theme: Theme;
}) {
  const { t } = useLocales();

  return (
    <Box
      display="grid"
      gridTemplateColumns="40px 2fr 1fr 1fr 1fr 40px"
      alignItems="center"
      px={2}
      py={1.5}
      bgcolor={theme.palette.background.paper}
      borderBottom={`1px solid ${theme.palette.divider}`}
      borderRadius="8px 8px 0 0"
    >
      {/* 1. Select All Checkbox */}
      <Checkbox checked={allSelected} onChange={onToggleAll} />
      {/* 2-6: Column Titles */}
      <Typography fontWeight={600}>المنتج</Typography>
      <Typography fontWeight={600} textAlign="center">
        {t('cart.price')}
      </Typography>
      <Typography fontWeight={600} textAlign="center">
        {t('cart.quantity')}
      </Typography>
      <Typography fontWeight={600} textAlign="center">
        {t('cart.total')}
      </Typography>
      <Box /> {/* Empty space for delete icon */}
    </Box>
  );
}
