import { ICartItem } from 'src/types/cart';

export interface CartItemType {
  id: number;
  title: string;
  price_for_selling: number;
  quantity: number;
  cover: string;
  selected?: boolean;
}
export interface CartItemProps {
  item: ICartItem;
  onQuantityChange: (delta: boolean) => void;
  onToggleSelect: () => void;
  onRemove: () => void;
}
export interface CartTotals {
  subtotal: number;
  selectedItems: number;
}
