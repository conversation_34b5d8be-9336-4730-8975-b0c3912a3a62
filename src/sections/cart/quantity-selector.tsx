// components/quantity-selector.tsx
import { Box, IconButton, Typography } from '@mui/material';
import { Icon } from '@iconify/react';
import { Theme } from '@mui/material/styles';

interface QuantitySelectorProps {
  onIncrement: () => void;
  onDecrement: () => void;
  theme: Theme;
  children: React.ReactNode;
}

const QuantitySelector = ({ onIncrement, onDecrement, children, theme }: QuantitySelectorProps) => (
  <Box display="flex" justifyContent="space-between" gap={3}>
    <IconButton
      onClick={onDecrement}
      size="small"
      sx={{
        bgcolor: `${theme.palette.grey[300]}`,
        borderRadius: '50%',
        '&:hover': { bgcolor: `${theme.palette.primary.main}` },
      }}
    >
      <Icon icon="mdi:minus" color={theme.palette.primary.dark} />
    </IconButton>
    <Typography>{children}</Typography>
    <IconButton
      onClick={onIncrement}
      size="small"
      sx={{
        bgcolor: `${theme.palette.primary.dark}`,
        borderRadius: '50%',
        '&:hover': { bgcolor: `${theme.palette.primary.main}` },
      }}
    >
      <Icon icon="mdi:plus" color={theme.palette.common.white} />
    </IconButton>
  </Box>
);

export default QuantitySelector;
