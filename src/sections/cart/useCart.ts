import { useEffect, useState } from 'react';
import { enqueueSnackbar } from 'notistack';
import { useCartStore } from 'src/store/user/useCartStore';
import { useCartAPIRequest } from 'src/api/cart';
import { useLocales } from 'src/locales';
import { ICartItem } from 'src/types/cart';

export const useCart = (cart: ICartItem[]) => {
  const { addBook, removeBook, deleteBooks } = useCartStore();
  const [items, setItems] = useState<ICartItem[]>(cart);

  useEffect(() => {
    setItems(cart);
  }, [cart]);

  const { handleRemoveFromCart } = useCartAPIRequest();
  const { t } = useLocales();

  const updateQuantity = (id: number, increasing: boolean) => {
    setItems((prevItems) =>
      prevItems.map((item) =>
        item.id === id
          ? {
              ...item,
              qty: increasing ? item.qty + 1 : Math.max(1, item.qty - 1),
            }
          : item
      )
    );

    if (increasing) {
      addBook(id, 1);
    } else {
      removeBook(id, 1);
    }
  };

  const removeItem = async (id: number) => {
    try {
      const res = await handleRemoveFromCart(id);
      if (res.success) {
        deleteBooks(id);
        setItems((prev) => prev.filter((item) => item.id !== id));
        enqueueSnackbar(t('cart.item_removed'), { variant: 'success' });
      } else {
        enqueueSnackbar(t('cart.error_removing_item'), { variant: 'error' });
      }
    } catch (error) {
      enqueueSnackbar(t('cart.error_removing_item'), { variant: 'error' });
      console.error('Error removing item from cart', error);
    }
  };

  const toggleSelection = (id: number, selectAll?: boolean) => {
    if (selectAll !== undefined) {
      setItems((prevItems) => prevItems.map((item) => ({ ...item, selected: selectAll })));
    } else {
      setItems((prevItems) =>
        prevItems.map((item) => (item.id === id ? { ...item, selected: !item.selected } : item))
      );
    }
  };

  return {
    items,
    updateQuantity,
    removeItem,
    toggleSelection,
  };
};
