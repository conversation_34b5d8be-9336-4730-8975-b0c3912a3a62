import { Box, Checkbox, IconButton, Typography } from '@mui/material';
import { useTheme } from '@mui/material/styles';
import Image from 'next/image';
import { Icon } from '@iconify/react';
import { fCurrency } from 'src/utils/format-number';
import QuantitySelector from './quantity-selector';

interface CartItemCardProps {
  product: {
    name: string;
    price: number;
    cover: string;
    quantity: number;
  };
  qty: number;
  selected: boolean;
  onQuantityChange: (increment: boolean) => void;
  onToggleSelect: () => void;
  onRemove: () => void;
  isMobile?: boolean;
}

const CartItemCard = ({
  product,
  qty,
  selected,
  onQuantityChange,
  onToggleSelect,
  onRemove,
  isMobile = false,
}: CartItemCardProps) => {
  const theme = useTheme();
  const { name, price, cover } = product;

  return (
    <Box
      sx={{
        borderRadius: 2,
        border: (() => {
          if (isMobile) {
            if (selected) {
              return `2px solid ${theme.palette.primary.light}`;
            }
            return `1px solid ${theme.palette.divider}`;
          }
          return 'none';
        })(),
        borderBottom: isMobile ? undefined : `1px solid ${theme.palette.divider}`,
        display: isMobile ? 'flex' : 'grid',
        gridTemplateColumns: isMobile ? 'none' : '40px 2fr 1fr 1fr 1fr 40px',
        alignItems: isMobile ? 'start' : 'center',
        flexDirection: isMobile ? 'column' : undefined,
        p: 2,
        gap: isMobile ? 2 : 0,
        position: 'relative',
        mb: isMobile ? 2 : 0,
        boxShadow: isMobile ? '0px 2px 8px rgba(0,0,0,0.05)' : 'none',
      }}
    >
      {/* Remove button in top-right for mobile */}
      {isMobile && (
        <IconButton
          onClick={onRemove}
          color="primary"
          sx={{
            position: 'absolute',
            top: 8,
            right: 8,
            border: `1px solid ${theme.palette.primary.main}`,
            borderRadius: '50%',
            width: 32,
            height: 32,
            '& svg': { fontSize: 20 },
            '&:hover': {
              color: theme.palette.error.main,
              borderColor: theme.palette.error.main,
              bgcolor: 'transparent',
            },
          }}
        >
          <Icon icon="mdi:close" />
        </IconButton>
      )}

      {/* Checkbox for desktop */}
      {!isMobile && <Checkbox checked={selected} onChange={onToggleSelect} />}

      {/* Product image and info */}
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'row',
          alignItems: 'center',
          gap: 2,
        }}
      >
        <Image src={cover} alt={name} width={60} height={80} style={{ objectFit: 'cover' }} />

        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 0.5 }}>
          <Typography width="80%" fontWeight="bold" fontSize={isMobile ? 13 : 'inherit'}>
            {name}
          </Typography>

          {isMobile && (
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Icon
                icon="mdi:tag-outline"
                width={16}
                height={16}
                color={theme.palette.primary.main}
              />
              <Typography color={theme.palette.primary.main} fontWeight="bold" fontSize={13}>
                {fCurrency(price)}
              </Typography>
            </Box>
          )}
        </Box>
      </Box>

      {/* Price - only show on desktop */}
      {!isMobile && (
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            gap: 1,
            color: theme.palette.primary.main,
          }}
        >
          <Icon icon="mdi:tag-outline" width={24} height={24} />
          <Typography fontWeight="bold">{fCurrency(price)}</Typography>
        </Box>
      )}

      {/* Quantity selector */}
      <Box
        sx={{
          mt: isMobile ? 1 : 0,
          display: 'flex',
          width: '100%',
          justifyContent: isMobile ? 'space-between' : 'center',
          alignItems: 'center',
        }}
      >
        <QuantitySelector
          onIncrement={() => onQuantityChange(true)}
          onDecrement={() => onQuantityChange(false)}
          theme={theme}
        >
          {qty}
        </QuantitySelector>
        {isMobile && (
          <Typography fontWeight="bold" textAlign="center">
            {fCurrency(price * qty)}
          </Typography>
        )}
      </Box>

      {/* Total - only desktop */}
      {!isMobile && (
        <Typography fontWeight="bold" textAlign="center">
          {fCurrency(price * qty)}
        </Typography>
      )}

      {/* Delete button - desktop */}
      {!isMobile && (
        <Box display="flex" justifyContent="center">
          <IconButton
            onClick={onRemove}
            color="primary"
            sx={{
              border: `1px solid ${theme.palette.primary.main}`,
              borderRadius: '50%',
              '&:hover': {
                color: theme.palette.error.main,
                borderColor: theme.palette.error.main,
                bgcolor: 'transparent',
              },
            }}
          >
            <Icon icon="mdi:close" width={24} height={24} />
          </IconButton>
        </Box>
      )}
    </Box>
  );
};

export default CartItemCard;
