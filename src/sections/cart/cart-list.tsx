'use client';

import {
  Box,
  Table,
  TableHead,
  TableBody,
  TableRow,
  TableCell,
  Divider,
  Checkbox,
  Skeleton,
  Typography,
} from '@mui/material';
import { useTheme } from '@mui/material/styles';
import { useGetPersonalCart } from 'src/api/cart';
import { useLocales } from 'src/locales';
import { useCart } from './useCart';
import CartItem from './cart-item';

const CartList = () => {
  const { cart, cartLoading, cartEmpty } = useGetPersonalCart();

  const theme = useTheme();
  const { items, updateQuantity, toggleSelection, removeItem } = useCart(cart);
  const { t } = useLocales();

  // Track if all items are selected
  const allSelected = items?.length > 0 && items.every((item) => item.selected);

  // Toggle all items' selection
  const handleSelectAll = (event: React.ChangeEvent<HTMLInputElement>) => {
    const { checked } = event.target;
    items.forEach((item) => toggleSelection(item.id, checked));
  };

  const columns = [t('cart.price'), t('cart.quantity'), t('cart.total')];

  if (cartEmpty) {
    return (
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: '100%',
        }}
      >
        <Typography variant="h6" align="center">
          {t('cart.empty')}
        </Typography>
      </Box>
    );
  }

  return (
    <Box>
      {cartLoading ? (
        <>
          <Skeleton variant="rectangular" width="100%" height={70} sx={{ mb: 2 }} />
          <Divider
            sx={{ flexGrow: 1, borderColor: theme.palette.grey[600], alignSelf: 'center', mb: 2 }}
          />
          <Skeleton variant="rectangular" width="100%" height={70} sx={{ mb: 2 }} />
          <Skeleton variant="rectangular" width="100%" height={70} sx={{ mb: 2 }} />
          <Skeleton variant="rectangular" width="100%" height={70} sx={{ mb: 2 }} />
          <Skeleton variant="rectangular" width="100%" height={70} sx={{ mb: 2 }} />
        </>
      ) : (
        <Table>
          <TableHead>
            <TableRow>
              <TableCell sx={{ bgcolor: 'transparent' }} padding="checkbox">
                <Checkbox
                  checked={allSelected}
                  indeterminate={items?.some((item) => item.selected) && !allSelected}
                  onChange={handleSelectAll}
                />
              </TableCell>
              <TableCell sx={{ bgcolor: 'transparent' }}>{t('cart.product')}</TableCell>
              {columns.map((column) => (
                <TableCell sx={{ bgcolor: 'transparent' }} align="center" key={column}>
                  {column}
                </TableCell>
              ))}
              <TableCell sx={{ bgcolor: 'transparent' }} />
            </TableRow>
            <Divider
              sx={{ flexGrow: 1, borderColor: theme.palette.grey[600], alignSelf: 'center' }}
            />
          </TableHead>
          <TableBody>
            {items?.map((item) => (
              <CartItem
                key={item.id}
                item={item}
                onQuantityChange={(delta) => updateQuantity(item.id, delta)}
                onToggleSelect={() => toggleSelection(item.id)}
                onRemove={() => removeItem(item.id)}
              />
            ))}
          </TableBody>
        </Table>
      )}
    </Box>
  );
};

export default CartList;
