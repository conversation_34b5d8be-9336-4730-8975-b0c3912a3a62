import { Box, IconButton, Typography } from '@mui/material';
import { Icon } from '@iconify/react';

interface QuantityControlProps {
  quantity: number;
  onIncrease: () => void;
  onDecrease: () => void;
}

const QuantityControl = ({ quantity, onIncrease, onDecrease }: QuantityControlProps) => (
  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
    <IconButton onClick={onIncrease}>
      <Icon icon="mdi:plus" />
    </IconButton>
    <Typography>{quantity}</Typography>
    <IconButton onClick={onDecrease}>
      <Icon icon="mdi:minus" />
    </IconButton>
  </Box>
);

export default QuantityControl;
