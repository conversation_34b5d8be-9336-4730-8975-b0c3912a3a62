'use client';

import { useState, useEffect } from 'react';
import { useTheme } from '@mui/material/styles';
import { Box, Skeleton, Typography, useMediaQuery } from '@mui/material';
import { useGetPersonalCart } from 'src/api/cart';
import { useLocales } from 'src/locales';
import CartItemCard from './cart-item-card';
import HeaderRow from './header-row';
import { useCart } from './useCart';

function CartListCards() {
  const theme = useTheme();
  const { t } = useLocales();
  const isMobile = useMediaQuery('(max-width:750px)');

  const { cart, cartLoading, cartEmpty } = useGetPersonalCart();
  const { items, updateQuantity, toggleSelection, removeItem } = useCart(cart || []);
  const [books, setBooks] = useState(items);

  useEffect(() => {
    setBooks(items);
  }, [items]);

  const toggleSelectAll = () => {
    const allSelected = books.every((b) => b.selected);
    const updatedBooks = books.map((b) => ({ ...b, selected: !allSelected }));
    setBooks(updatedBooks);
  };

  const handleQuantityChange = (id: number, increment: boolean) => {
    const updatedBooks = books.map((b) =>
      b.id === id
        ? {
            ...b,
            qty: Math.max(1, b.qty + (increment ? 1 : -1)),
          }
        : b
    );
    setBooks(updatedBooks);
    updateQuantity(id, increment);
  };

  const handleToggleSelect = (id: number) => {
    const updatedBooks = books.map((b) => (b.id === id ? { ...b, selected: !b.selected } : b));
    setBooks(updatedBooks);
    toggleSelection(id);
  };

  const handleRemove = (id: number) => {
    const updatedBooks = books.filter((b) => b.id !== id);
    setBooks(updatedBooks);
    removeItem(id);
  };

  if (cartLoading) {
    return (
      <Box style={{ maxWidth: '1226px', margin: '2rem auto' }}>
        <Skeleton variant="rectangular" width="100%" height={100} sx={{ my: 2 }} />
        <Skeleton variant="rectangular" width="100%" height={100} sx={{ my: 2 }} />
        <Skeleton variant="rectangular" width="100%" height={100} sx={{ my: 2 }} />
      </Box>
    );
  }

  if (cartEmpty) {
    return (
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: '100%',
        }}
      >
        <Typography variant="h6" align="center">
          {t('cart.empty')}
        </Typography>
      </Box>
    );
  }

  return (
    <Box style={{ maxWidth: '1226px', margin: '2rem auto' }}>
      {!isMobile && (
        <HeaderRow
          allSelected={books.length > 0 && books.every((b) => b.selected)}
          onToggleAll={toggleSelectAll}
          theme={theme}
        />
      )}

      {books.map((book) => (
        <CartItemCard
          key={book.id}
          product={book.product}
          qty={book.qty}
          selected={book.selected}
          onQuantityChange={(increment) => handleQuantityChange(book.id, increment)}
          onToggleSelect={() => handleToggleSelect(book.id)}
          onRemove={() => handleRemove(book.id)}
          isMobile={isMobile}
        />
      ))}
    </Box>
  );
}

export default CartListCards;
