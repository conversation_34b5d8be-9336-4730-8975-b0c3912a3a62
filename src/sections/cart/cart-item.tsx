import { TableRow, TableCell, Checkbox, IconButton, Typography, Box } from '@mui/material';
import { Icon } from '@iconify/react';
import Image from 'next/image';
import { useTheme } from '@mui/material/styles';
import { fCurrency } from 'src/utils/format-number';
import QuantitySelector from 'src/layouts/product-details/quantity-selector';
import { CartItemProps } from './types';

const CartItem = ({ item, onQuantityChange, onToggleSelect, onRemove }: CartItemProps) => {
  const { product, qty, selected = true } = item;
  const theme = useTheme();
  return (
    <TableRow>
      <TableCell padding="checkbox">
        <Checkbox checked={selected} onChange={onToggleSelect} />
      </TableCell>
      <TableCell>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <Image
            src={product.cover}
            alt={product.name}
            width={80}
            height={80}
            style={{ objectFit: 'cover' }}
          />
          <Typography>{product.name}</Typography>
        </Box>
      </TableCell>
      <TableCell align="center">
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            gap: 1,
            color: theme.palette.primary.main,
          }}
        >
          <Icon icon="mdi:tag-outline" width={24} height={24} />
          <Typography fontWeight="bold">{fCurrency(product.price)}</Typography>
        </Box>
      </TableCell>
      <TableCell align="center">
        <QuantitySelector
          onIncrement={() => onQuantityChange(true)}
          onDecrement={() => onQuantityChange(false)}
        >
          {qty}
        </QuantitySelector>
      </TableCell>
      <TableCell align="center">
        <Typography>{fCurrency(item.product.price * item.qty)}</Typography>
      </TableCell>
      <TableCell>
        <IconButton
          onClick={onRemove}
          color="primary"
          sx={{
            border: `1px solid ${theme.palette.primary.main}`,
            borderRadius: '50%',
            '&:hover': {
              color: theme.palette.error.main,
              borderColor: theme.palette.error.main,
              bgcolor: 'transparent',
            },
          }}
        >
          <Icon icon="mdi:close" width="24" height="24" />
        </IconButton>
      </TableCell>
    </TableRow>
  );
};

export default CartItem;
