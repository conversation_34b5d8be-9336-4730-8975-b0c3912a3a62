'use client';

import { Stack } from '@mui/material';

import ContactDetails from './contact-details';

const ContactUsSection = () => (
  <Stack
    direction="column"
    sx={{
      width: '100%',
      maxWidth: '1640px',
      mb: { xs: '120px', md: '234px' },
      gap: { xs: '40px', md: '65px' },
    }}
  >
    {/* Contact Form & Details */}
    <Stack
      direction={{ xs: 'column', md: 'row' }}
      gap={4}
      sx={{
        maxWidth: '1640px',
        maxHeight: { xs: 'auto', md: '591px' },
        py: 4,
      }}
    >
      <ContactDetails />
    </Stack>

    {/* Section Header with Divider */}
    {/* <Stack
      sx={{ justifyContent: 'space-between', alignItems: 'center' }}
      direction="row"
      gap="34px"
    >
      <Typography
        sx={{
          typography: { xs: 'h5', md: 'h3' }, // Responsive variant
          whiteSpace: 'nowrap',
          color: theme.palette.primary.dark,
        }}
      >
        {t('contact_us.our_location_on_map')}
      </Typography>

      <Divider
        sx={{
          flexGrow: 1,
          borderColor: theme.palette.grey[600],
        }}
      />
    </Stack> */}

    {/* Map Section */}
    {/* <LocationOnMap /> */}
  </Stack>
);

export default ContactUsSection;
