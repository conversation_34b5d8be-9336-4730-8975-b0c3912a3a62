'use client';

import { <PERSON>, Divider, IconButton, Stack, Typography } from '@mui/material';
import { useTheme } from '@mui/material/styles';
import { useLocales } from 'src/locales';
import Iconify from 'src/components/iconify';
import { _socials } from 'src/_mock/_others';

const ContactDetails = () => {
  const theme = useTheme();
  const { t } = useLocales();

  return (
    <Box
      sx={{
        bgcolor: theme.palette.background.paper,
        // height: '591px',
        maxWidth: '836px',
        width: '100%',
        borderRadius: '30px',
        px: '36px',
        pt: '52px',
        pb: '46px',
      }}
    >
      <Stack
        sx={{
          mb: 3,
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'start',
        }}
      >
        <Typography variant="h4">{t('contact_us.contact_us')}</Typography>
        <Divider sx={{ my: 2, width: '100%' }} />
        <Stack
          gap={8}
          sx={{ display: 'flex', flexDirection: 'column', justifyContent: 'space-between' }}
        >
          <Stack gap={2}>
            <Stack direction="row" alignItems="center" gap={2}>
              <IconButton
                target="_blank"
                href="https://wa.me/+963944214416"
                sx={{
                  width: '50px',
                  height: '50px',
                  bgcolor: theme.palette.primary.dark,
                  '&:hover': {
                    bgcolor: theme.palette.primary.main,
                  },
                }}
              >
                {' '}
                <Iconify
                  icon="mdi:whatsapp"
                  width={35}
                  height={35}
                  sx={{
                    color: theme.palette.common.white,
                    p: 1,
                  }}
                />
              </IconButton>
              <Typography
                sx={{
                  color: 'primary.dark',
                  fontWeight: '700',
                  fontSize: '20px',
                  lineHeight: '24px',
                }}
              >
                {t('contact_us.customer_support')}
                <Typography
                  sx={{
                    color: 'primary.dark',
                    fontWeight: '400',
                    fontSize: '16px',
                    lineHeight: '20px',
                  }}
                >
                  +963944214416
                </Typography>
              </Typography>
            </Stack>
            <Stack direction="row" alignItems="center" gap={2}>
              <IconButton
                target="_blank"
                href="mailto:<EMAIL>"
                sx={{
                  width: '50px',
                  height: '50px',
                  bgcolor: theme.palette.primary.dark,
                  '&:hover': {
                    bgcolor: theme.palette.primary.main,
                  },
                }}
              >
                {' '}
                <Iconify
                  icon="mdi:mail-outline"
                  width={35}
                  height={35}
                  sx={{
                    color: theme.palette.common.white,
                    p: 1,
                  }}
                />
              </IconButton>
              <Typography
                sx={{
                  color: 'primary.dark',
                  fontWeight: '700',
                  fontSize: '20px',
                  lineHeight: '24px',
                  display: 'flex',
                  flexDirection: 'column',
                }}
              >
                {t('contact_us.email')}
                <Typography
                  sx={{
                    color: 'primary.dark',
                    fontWeight: '400',
                    fontSize: '16px',
                    lineHeight: '20px',
                  }}
                >
                  <EMAIL>
                </Typography>
              </Typography>
            </Stack>
          </Stack>
          <Stack sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
            <Typography
              sx={{
                color: 'primary.dark',
                fontWeight: '700',
                fontSize: '20px',
                lineHeight: '24px',
                mt: 2,
              }}
            >
              {t('contact_us.social_media')}
            </Typography>
            <Stack direction="row" gap={2} sx={{ mt: 2 }}>
              {_socials.map((social) => (
                <Box key={social.name} sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
                  <a target="_blank" href={social.path} rel="noreferrer">
                    <IconButton
                      sx={{
                        width: '50px',
                        height: '50px',
                        bgcolor: theme.palette.primary.dark,
                        '&:hover': {
                          bgcolor: theme.palette.primary.main,
                        },
                      }}
                    >
                      <Iconify
                        sx={{
                          color: theme.palette.common.white,
                          p: 1,
                        }}
                        icon={social.icon}
                        width={35}
                        height={35}
                      />
                    </IconButton>
                  </a>
                </Box>
              ))}
            </Stack>
          </Stack>
        </Stack>
      </Stack>
    </Box>
  );
};

export default ContactDetails;
