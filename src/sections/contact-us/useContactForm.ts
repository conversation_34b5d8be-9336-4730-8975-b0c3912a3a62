import { useForm } from 'react-hook-form';
import * as yup from 'yup';
import { yupResolver } from '@hookform/resolvers/yup';

const schema = yup.object({
  firstName: yup.string().required('الاسم الأول مطلوب'),
  lastName: yup.string().required('الاسم الثاني مطلوب'),
  email: yup.string().email('البريد الإلكتروني غير صحيح').required('البريد الإلكتروني مطلوب'),
  phone: yup
    .string()
    .matches(/^[0-9]+$/, 'رقم الهاتف غير صحيح')
    .required('رقم الهاتف مطلوب'),
  message: yup.string().required('الرسالة مطلوبة'),
});

export type ContactFormData = yup.InferType<typeof schema>;

export const useContactForm = () => {
  const {
    register,
    handleSubmit,
    formState: { errors, dirtyFields },
  } = useForm<ContactFormData>({
    resolver: yupResolver(schema),
  });

  const onSubmit = (data: ContactFormData) => {
    console.log('Form submitted:', data);
  };

  return {
    register,
    handleSubmit: handleSubmit(onSubmit),
    errors,
    dirtyFields,
  };
};
