import { create } from 'zustand';
import { PaymentFormData } from 'src/sections/checkout/payment-data/types';

interface PaymentStore {
  formData: PaymentFormData;
  setFormData: (data: Partial<PaymentFormData>) => void;
  resetForm: () => void;
}

const initialState: PaymentFormData = {
  paymentMethod: 'mastercard',
  cardNumber: '',
  cardHolder: '',
  expiryDate: '',
  cvv: '',
  paypalEmail: '',
  paypalPhone: '',
};

export const usePaymentStore = create<PaymentStore>()((set) => ({
  formData: initialState,
  setFormData: (data) =>
    set((state) => ({
      formData: { ...state.formData, ...data },
    })),
  resetForm: () => set({ formData: initialState }),
}));
