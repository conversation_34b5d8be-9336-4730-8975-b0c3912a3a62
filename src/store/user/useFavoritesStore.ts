import { create } from 'zustand';
import { devtools } from 'zustand/middleware';

interface FavoriteItem {
  id: string;
  title: string;
  price: number;
  image: string;
}

interface FavoritesStore {
  items: FavoriteItem[];
  addItem: (item: FavoriteItem) => void;
  removeItem: (id: string) => void;
  clearFavorites: () => void;
}

export const useFavoritesStore = create<FavoritesStore>()(
  devtools((set) => ({
    items: [],
    addItem: (item) =>
      set((state) => ({
        items: [...state.items, item],
      })),
    removeItem: (id) =>
      set((state) => ({
        items: state.items.filter((i) => i.id !== id),
      })),
    clearFavorites: () => set({ items: [] }),
  }))
);
