import { create } from 'zustand';
import { IUserCartStore } from './types';

export const useCartStore = create<IUserCartStore>()((set, get) => ({
  cart: [] as { id: number; quantity: number; price: number }[],
  totalPrice: 0,
  cartLength: 0,

  // Initialize the cart with provided data
  initializeCart: (initialCart: { id: number; quantity: number; price: number }[]) => {
    set({ cart: initialCart, ...get().updateCartMetrics(initialCart) });
  },

  // Helper function to update total price and cart length
  updateCartMetrics: (cart: { id: number; quantity: number; price: number }[]) => {
    const totalPrice = cart.reduce((acc, item) => acc + item.quantity * item.price, 0); // Replace 10 with actual price logic
    const cartLength = cart.reduce((acc, item) => acc + item.quantity, 0);
    return { totalPrice, cartLength };
  },

  // Add or update book quantity in the cart
  addBook: (id: number, quantity: number, price?) =>
    set((state) => {
      const bookIndex = state.cart.findIndex((item) => item.id === id);
      const updatedCart = [...state.cart];

      if (bookIndex !== -1) {
        updatedCart[bookIndex].quantity += quantity;
      } else {
        updatedCart.push({ id, quantity, price: price! });
      }

      return { cart: updatedCart, ...get().updateCartMetrics(updatedCart) };
    }),

  // Remove a specific quantity of a book or remove it entirely if quantity is 0
  removeBook: (id: number, quantity: number) =>
    set((state) => {
      const updatedCart = state.cart
        .map((item) =>
          item.id === id ? { ...item, quantity: Math.max(0, item.quantity - quantity) } : item
        )
        .filter((item) => item.quantity > 0);
      return { cart: updatedCart, ...get().updateCartMetrics(updatedCart) };
    }),

  // Delete books by ID
  deleteBooks: (id: number) =>
    set((state) => {
      const updatedCart = state.cart.filter((item) => item.id !== id);
      return { cart: updatedCart, ...get().updateCartMetrics(updatedCart) };
    }),

  // Clear the entire cart
  clearCart: () => set({ cart: [], totalPrice: 0, cartLength: 0 }),
}));
