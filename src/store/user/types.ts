export interface IUserCartStore {
  cart: { id: number; quantity: number; price: number }[];
  totalPrice: number;
  cartLength: number;
  initializeCart: (cart: { id: number; quantity: number; price: number }[]) => void;
  addBook: (id: number, quantity: number, price?: number) => void;
  updateCartMetrics: (cart: { id: number; quantity: number; price: number }[]) => {
    totalPrice: number;
    cartLength: number;
  };
  removeBook: (id: number, quantity: number) => void;
  deleteBooks: (id: number) => void;
  clearCart: () => void;
}
