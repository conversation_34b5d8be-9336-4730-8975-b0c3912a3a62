// ----------------------------------------------------------------------

export type IProductFilterValue = string | string[] | number | number[];

export type IProductFilters = {
  rating: string;
  gender: string[];
  category: string;
  colors: string[];
  priceRange: number[];
};

// ----------------------------------------------------------------------

export type IProductReviewNewForm = {
  rating: number | null;
  review: string;
  name: string;
  email: string;
};

export type IProductTableFilterValue = string | string[];

export type IProductTableFilters = {
  name: string;
  stock: string[];
  publish: string[];
};

export interface IProductItem {
  id: number;
  is_favourite: boolean;
  image: string;
  images: string[];
  slug: string;
  name: string;
  category: Category;
  cover: string;
  author: any;
  price_for_selling: number;
  alt_image: string;
  alt_cover: string;
  quantity?: number;
  short_description: string;
  key_words: string;
  robot: any;
  has_discount: boolean;
  discount: number;
  total_review_precent: TotalReviewPrecent;
  related_products: RelatedProducts;
  available: boolean;
}

export interface Category {
  id: number;
  name: string;
  image: string;
  alt_image: string;
  alt_cover: string;
  short_description: string;
  key_words: string;
  description: string;
  icon: string;
}

export interface TotalReviewPrecent {
  total_reviews: number;
  rating: number;
  stars_reviews: number[];
}

export interface RelatedProducts {
  title: Title;
  items: IProductItem[];
}

export interface Title {
  value: string;
}

export interface IProductReview {
  data: IReview[];
  pagination_info: IPaginationInfo;
}

export interface IReview {
  id: number;
  name: string;
  image: string;
  comment: string;
  rating: number;
  added_at: string;
}

export interface IPaginationInfo {
  total: number;
  count: number;
  per_page: number;
  current_page: number;
  total_pages: number;
  previous: number;
}
