// ----------------------------------------------------------------------

import { IPaginationInfo } from './product';

export type IOrderTableFilterValue = string | Date | null;

export type IOrderTableFilters = {
  name: string;
  status: string;
  startDate: Date | null;
  endDate: Date | null;
};

// ----------------------------------------------------------------------

export type IOrderHistory = {
  orderTime: Date;
  paymentTime: Date;
  deliveryTime: Date;
  completionTime: Date;
  timeline: {
    title: string;
    time: Date;
  }[];
};

export type IOrderShippingAddress = {
  fullAddress: string;
  phoneNumber: string;
};

export type IOrderPayment = {
  cardType: string;
  cardNumber: string;
};

export type IOrderDelivery = {
  shipBy: string;
  speedy: string;
  trackingNumber: string;
};

export type IOrderCustomer = {
  id: string;
  name: string;
  email: string;
  avatarUrl: string;
  ipAddress: string;
};

export type IOrderProductItem = {
  id: string;
  sku: string;
  name: string;
  price: number;
  coverUrl: string;
  quantity: number;
};

export type IOrderItem = {
  id: string;
  taxes: number;
  status: string;
  shipping: number;
  discount: number;
  subTotal: number;
  orderNumber: string;
  totalAmount: number;
  totalQuantity: number;
  history: IOrderHistory;
  customer: IOrderCustomer;
  delivery: IOrderDelivery;
  items: IOrderProductItem[];
  createdAt: Date;
};
// export interface IOrder {
//   id: number;
//   created_at: string;
//   payment_method: string;
//   status: string;
//   total: number;
//   confirmed: boolean;
// }

export interface IOrder {
  id: number;
  number: number;
  created_at: string;
  total_items: number;
  products: IProduct[];
  total: any;
  currency: string;
  status: Status;
  payment_method: string;
  payment: any;
  review: Review2;
  confirmed: boolean;
}

interface IProduct {
  id: number;
  name: string;
  images: any;
  qty: any;
  price: number;
  currency: string;
  total_reviews: number;
  review: Review;
}

export interface Review {
  rating?: number;
  status: string;
}

export interface Status {
  value: string;
  label: string;
  color: string;
}

export interface Review2 {
  rating: any;
  status: string;
}
