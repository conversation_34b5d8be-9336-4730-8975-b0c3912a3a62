export interface ICart {
  msg: string;
  data: ICartData;
}

export interface ICartData {
  sub_total: number;
  tax_amount: any;
  coupon: any;
  coupon_code: any;
  discount_percentage: any;
  discount_amount: any;
  total: number;
  currency: string;
  total_items: number;
  count_selected: number;
  items: ICartItems;
}

export interface ICartItems {
  data: ICartItem[];
}

export interface ICartItem {
  id: number;
  selected: boolean;
  product_id: number;
  coupon_id: any;
  qty: number;
  product_price: number;
  total_price: number;
  discount: number;
  is_selected: number;
  product: IProduct;
}

export interface IProduct {
  id: number;
  name: string;
  category_name?: string;
  quantity: number;
  discount: string;
  discount_type: string;
  price: number;
  cover: string;
  image: string;
}
