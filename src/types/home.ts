import { IProductItem } from './product';
import { ICategory } from './shared';

export interface IHomeData {
  banners: Banner[];
  categories: ICategory[];
  best_sellers: BestSeller[];
  new_products: NewProduct[];
  products_by_category: ProductsByCategory[];
}

export interface Banner {
  title: string;
  description: string;
  link: string;
  image: string;
  type?: string;
}

export interface BestSeller {
  id: number;
  is_favourite: any;
  image: any;
  slug: string;
  name: string;
  category: any;
  cover: any;
  author: any;
  price_for_selling: string;
  alt_image: any;
  alt_cover: any;
  short_decription: any;
  key_words: any;
  robot: any;
  has_discount: boolean;
  discount: any;
  discount_start: string;
  discount_end: string;
}

export interface NewProduct {
  id: number;
  is_favourite: any;
  image: any;
  slug: string;
  name: string;
  category: any;
  cover: any;
  author: any;
  price_for_selling: string;
  alt_image: any;
  alt_cover: any;
  short_decription: any;
  key_words: any;
  robot: any;
  has_discount: boolean;
  discount: any;
  discount_start: string;
  discount_end: string;
}

export interface ProductsByCategory {
  category_id?: number;
  category_name: string;
  products?: IProductItem[];
}
