import { Container, Grid } from '@mui/material';
import PageHeader from 'src/components/page-header';
import OrderDetails from 'src/sections/checkout/order-details';
import PaymentData from 'src/sections/checkout/payment-data';

export default function PaymentPage() {
  return (
    <Container
      maxWidth={false}
      sx={{
        maxWidth: '1639px',
        py: 4,
      }}
    >
      <PageHeader title="شراء المنتجات" breadcrumbsPath={[{ name: 'السلة', path: 'cart' }]} />
      <Grid container spacing={2}>
        <Grid item xs={12} md={8}>
          <Container
            maxWidth={false}
            sx={{
              maxWidth: '1226px',
              pl: { xs: 2, md: 0 },
            }}
          >
            <PaymentData />
          </Container>
        </Grid>
        <Grid item xs={12} md={4}>
          <OrderDetails />
        </Grid>
      </Grid>
    </Container>
  );
}
