import { Container, Grid } from '@mui/material';
import PageHeader from 'src/components/page-header';
import CartList from 'src/sections/cart';
import OrderDetails from 'src/sections/checkout/order-details';

export default function CartPage() {
  return (
    <Container
      maxWidth={false}
      sx={{
        maxWidth: '1639px',
        py: 4,
      }}
    >
      <PageHeader title="السلة" />
      <Grid container spacing={2}>
        <Grid sx={{ bgcolor: '#FFF', borderRadius: '30px' }} item xs={12} md={8}>
          <Container
            maxWidth={false}
            sx={{
              height: '100%',
              maxWidth: '1226px',
              pl: { xs: 2, md: 0 },
            }}
          >
            <CartList />
          </Container>
        </Grid>
        <Grid item xs={12} md={4}>
          <OrderDetails />
        </Grid>
      </Grid>
    </Container>
  );
}
