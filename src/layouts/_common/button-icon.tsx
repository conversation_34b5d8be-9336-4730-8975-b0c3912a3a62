import { Box } from '@mui/material';
import React from 'react';
import Iconify from 'src/components/iconify';
import { useResponsive } from 'src/hooks/use-responsive';

type IProps = {
  icon: string;
  sx?: any;
};
export default function IconHeader({ icon, sx }: IProps) {
  const mdUp = useResponsive('up', 'md');

  return (
    <Box
      sx={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        width: mdUp ? '40px' : '30px',
        height: mdUp ? '40px' : '30px',
        borderRadius: '100%',
        border: (theme) => `1px solid ${theme.palette.divider}`,
        '&:hover': {
          border: (theme) => `1px solid ${theme.palette.primary.main}`,
          '& svg': {
            color: (theme) => theme.palette.primary.main,
          },
        },
        ...sx,
      }}
    >
      <Iconify
        icon={icon}
        width={{ sx: 18, md: 22 }}
        height={{ sx: 18, md: 22 }}
        sx={{
          color: (theme) => theme.palette.text.secondary,
          '&:hover': {
            color: (theme) => theme.palette.primary.main,
          },
        }}
      />
    </Box>
  );
}
