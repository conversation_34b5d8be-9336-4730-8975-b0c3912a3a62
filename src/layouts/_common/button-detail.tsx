import { Button, ButtonProps } from '@mui/material';
import { SxProps, Theme } from '@mui/material/styles';

interface CustomButtonProps extends ButtonProps {
  sx?: SxProps<Theme>;
}

const CustomButton = ({ sx, ...props }: CustomButtonProps) => {
  const defaultStyles: SxProps<Theme> = {
    borderRadius: (theme) => theme.shape.borderRadius,
    padding: (theme) => theme.spacing(1, 2),
    textTransform: 'none',
    fontWeight: 500,
    ...sx,
  };

  return <Button {...props} sx={defaultStyles} />;
};

export default CustomButton;
