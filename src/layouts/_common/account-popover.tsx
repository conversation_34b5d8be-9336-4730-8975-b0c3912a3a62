import { m } from 'framer-motion';
// @mui
import Box from '@mui/material/Box';
import Stack from '@mui/material/Stack';
import Avatar from '@mui/material/Avatar';
import Divider from '@mui/material/Divider';
import MenuItem from '@mui/material/MenuItem';
import IconButton from '@mui/material/IconButton';
import Typography from '@mui/material/Typography';
// routes
// import { paths } from 'src/routes/paths';
import { useRouter } from 'src/routes/hooks';
// hooks

// auth
import { useAuthContext } from 'src/auth/hooks';
// components
import { varHover } from 'src/components/animate';
import { useSnackbar } from 'src/components/snackbar';
import CustomPopover, { usePopover } from 'src/components/custom-popover';
import Iconify from 'src/components/iconify';
import { useResponsive } from 'src/hooks/use-responsive';
import { paths } from 'src/routes/paths';
import { useLocales } from 'src/locales';

// ----------------------------------------------------------------------

const OPTIONS = (t: any) => [
  {
    label: t('common.profile'),
    linkTo: '/profile',
  },
  {
    label: t('common.home'),
    linkTo: '/',
  },
];

// ----------------------------------------------------------------------

export default function AccountPopover() {
  const router = useRouter();
  const mdUp = useResponsive('up', 'md');
  const { user, logout } = useAuthContext();
  const { enqueueSnackbar } = useSnackbar();
  const popover = usePopover();
  const { t } = useLocales();

  const handleLogout = async () => {
    try {
      await logout();
      popover.onClose();
      router.replace('/');
    } catch (error) {
      console.error(error);
      enqueueSnackbar('Unable to logout!', { variant: 'error' });
    }
  };

  const handleClickItem = (path: string) => {
    popover.onClose();
    router.push(path);
  };

  return (
    <>
      <IconButton
        component={m.button}
        whileTap="tap"
        whileHover="hover"
        variants={varHover(1.05)}
        onClick={user ? popover.onOpen : () => router.push(paths.auth.login)}
        sx={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          width: mdUp ? '40px' : '30px',
          height: mdUp ? '40px' : '30px',
          borderRadius: '100%',
          border: (theme) => `1px solid ${theme.palette.divider}`,
          '&:hover': {
            border: (theme) => `1px solid ${theme.palette.primary.main}`,
            '& svg': {
              color: (theme) => theme.palette.primary.main,
            },
          },
          ...(popover.open && {
            border: (theme) => `1px solid ${theme.palette.primary.main}`,
            background: (theme) => theme.palette.action.focusOpacity,
            '& svg': {
              color: (theme) => theme.palette.primary.main,
            },
          }),
        }}
      >
        {user ? (
          <Avatar
            src={user?.user?.image}
            alt={user?.user?.first_name}
            sx={{
              width: 26,
              height: 26,
              border: (theme) => `solid 2px ${theme.palette.background.default}`,
            }}
          />
        ) : (
          <Iconify
            icon="lucide:user"
            width={{ sx: 20, md: 26 }}
            height={{ sx: 20, md: 26 }}
            sx={{
              color: (theme) => theme.palette.text.secondary,
              '&:hover': {
                color: (theme) => theme.palette.primary.main,
              },
            }}
          />
        )}
      </IconButton>

      <CustomPopover open={popover.open} onClose={popover.onClose} sx={{ width: 200, p: 0 }}>
        <Box sx={{ p: 2, pb: 1.5 }}>
          <Typography variant="subtitle2" noWrap>
            {user?.user?.first_name}
          </Typography>

          <Typography variant="body2" sx={{ color: 'text.secondary' }} noWrap>
            {user?.user?.email}
          </Typography>
        </Box>

        <Divider sx={{ borderStyle: 'dashed' }} />

        <Stack sx={{ p: 1 }}>
          {OPTIONS(t).map((option) => (
            <MenuItem key={option.label} onClick={() => handleClickItem(option.linkTo)}>
              {option.label}
            </MenuItem>
          ))}
        </Stack>

        <Divider sx={{ borderStyle: 'dashed' }} />

        <MenuItem
          onClick={handleLogout}
          sx={{ m: 1, fontWeight: 'fontWeightBold', color: 'error.main' }}
        >
          {t('auth.logout')}
        </MenuItem>
      </CustomPopover>
    </>
  );
}
