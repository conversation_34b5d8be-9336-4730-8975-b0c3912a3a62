// @mui
import { useTheme } from '@mui/material/styles';
import Stack from '@mui/material/Stack';
import AppBar from '@mui/material/AppBar';
import Toolbar from '@mui/material/Toolbar';
import Container from '@mui/material/Container';
import { Badge, Divider, IconButton } from '@mui/material';
// hooks
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { useOffSetTop } from 'src/hooks/use-off-set-top';
import { useResponsive } from 'src/hooks/use-responsive';
// theme
import { bgBlur } from 'src/theme/css';

// components
import Logo from 'src/components/logo';
// routes
import { useCartStore } from 'src/store/user/useCartStore';
import { paths } from 'src/routes/paths';
import SearchOverlay from 'src/components/searchBar';
import { HEADER } from '../config-layout';
import { NavConfig } from './config-navigation';
import NavMobile from './nav/mobile';
import NavDesktop from './nav/desktop';

//
import { AccountPopover, HeaderShadow, LanguagePopover } from '../_common';
import IconHeader from '../_common/button-icon';

// ----------------------------------------------------------------------

export default function Header() {
  const theme = useTheme();

  const mdUp = useResponsive('up', 'md');

  const offsetTop = useOffSetTop(HEADER.H_DESKTOP);
  const { cartLength } = useCartStore();
  const router = useRouter();
  const [showSearch, setShowSearch] = useState(false);

  return (
    <AppBar>
      <Toolbar
        disableGutters
        sx={{
          height: {
            xs: HEADER.H_MOBILE,
            md: HEADER.H_DESKTOP,
          },
          transition: theme.transitions.create(['height'], {
            easing: theme.transitions.easing.easeInOut,
            duration: theme.transitions.duration.shorter,
          }),
          bgcolor: theme.palette.background.paper,
          ...(offsetTop && {
            ...bgBlur({
              color: theme.palette.background.paper,
            }),
            height: {
              md: HEADER.H_DESKTOP_OFFSET,
            },
          }),
        }}
      >
        <Container
          sx={{
            height: 1,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between ',
            maxWidth: '91% !important',
          }}
        >
          <Logo
            sx={{
              ...(!mdUp && {
                width: 60,
              }),
            }}
          />

          {mdUp && <NavDesktop offsetTop={offsetTop} data={NavConfig()} />}

          <Stack alignItems="center" direction={{ xs: 'row', md: 'row' }} spacing="8px">
            {/* <IconHeader icon="ph:currency-dollar" /> */}
            <LanguagePopover />
            <IconButton onClick={() => setShowSearch(!showSearch)}>
              <IconHeader icon="tabler:search" />
            </IconButton>
            {showSearch && <SearchOverlay open={showSearch} onClose={() => setShowSearch(false)} />}

            <IconButton onClick={() => router.push(paths.cart)}>
              <Badge
                badgeContent={cartLength}
                color="secondary"
                anchorOrigin={{
                  vertical: 'top',
                  horizontal: 'left',
                }}
              >
                <IconHeader icon="fluent:cart-20-regular" />
              </Badge>
            </IconButton>

            <Divider
              flexItem
              sx={{
                borderColor: () => theme.palette.divider, // Change the color of the divider
                borderWidth: 1, // Adjust the thickness of the divider
                borderRadius: 1, // Make the edges rounded
                marginX: 0, // Add some horizontal margin
              }}
            />

            <AccountPopover />

            {!mdUp && <NavMobile offsetTop={offsetTop} data={NavConfig()} />}
          </Stack>
        </Container>
      </Toolbar>

      {offsetTop && <HeaderShadow />}
    </AppBar>
  );
}
