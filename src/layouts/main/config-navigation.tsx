// routes

import { useLocales } from 'src/locales';
import { paths } from 'src/routes/paths';

// ----------------------------------------------------------------------
export const NavConfig = () => {
  const { t } = useLocales();

  return [
    {
      title: t('nav.news'),
      path: '#',
    },
    {
      title: t('nav.issues_list'),
      path: paths.comingSoon,
    },
    {
      title: t('nav.nasq'),
      path: paths.comingSoon,
    },
  ];
};
