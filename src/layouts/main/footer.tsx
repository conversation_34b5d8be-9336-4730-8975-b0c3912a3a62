// @mui
import Box from '@mui/material/Box';
import Link from '@mui/material/Link';
import Stack from '@mui/material/Stack';
import Container from '@mui/material/Container';
import Grid from '@mui/material/Unstable_Grid2';
import IconButton from '@mui/material/IconButton';
import Typography from '@mui/material/Typography';
// routes
import { useTheme } from '@mui/material/styles';
import { paths } from 'src/routes/paths';
import { RouterLink } from 'src/routes/components';
// _mock
import { _socials } from 'src/_mock';
// components
import Logo from 'src/components/logo';
import Iconify from 'src/components/iconify';
import { useLocales } from 'src/locales';

// ----------------------------------------------------------------------

export default function Footer() {
  const { t } = useLocales();
  const LINKS = [
    {
      headline: t('footer.links.headline'),
      children: [
        { name: t('footer.links.about'), href: paths.about },
        { name: t('footer.links.contact'), href: paths.contact },
        { name: t('footer.links.releases_list'), href: paths.comingSoon },
        { name: t('footer.links.distributors'), href: paths.comingSoon },
        { name: t('footer.links.terms'), href: paths.comingSoon },
      ],
    },
    {
      headline: t('footer.links.friends.headline'),
      children: [
        { name: t('footer.links.friends.1'), href: paths.comingSoon },
        { name: t('footer.links.friends.2'), href: paths.comingSoon },
      ],
    },
  ];
  const theme = useTheme();
  const mainFooter = (
    <Box
      component="footer"
      sx={{
        position: 'relative',
        bgcolor: '#242963',
      }}
    >
      <Container
        maxWidth="xl"
        sx={{
          pt: 7,
          pb: 14.5,
          maxWidth: '92% !important',
          textAlign: { xs: 'center', md: 'unset' },
        }}
      >
        <Grid
          container
          justifyContent={{
            xs: 'center',
            md: 'space-between',
          }}
        >
          <Grid xs={8} md={4}>
            <Stack spacing={1}>
              <Stack direction="row" spacing={1} alignItems="center">
                <Logo sx={{ mb: 1 }} color="#ffffff" />
                <Typography
                  variant="body2"
                  color="common.white"
                  sx={{
                    maxWidth: 400,
                    mx: { xs: 'auto', md: 'unset' },
                  }}
                >
                  {t('footer.description')}
                </Typography>
              </Stack>
              <Stack
                direction="row"
                spacing={1}
                justifyContent={{ xs: 'center', md: 'flex-start' }}
                sx={{
                  mb: { xs: 5, md: 0 },
                }}
              >
                {_socials.map((social) => (
                  <a target="_blank" href={social.path} rel="noreferrer">
                    <IconButton
                      key={social.name}
                      sx={{
                        width: '50px',
                        height: '50px',
                      }}
                    >
                      <Iconify
                        sx={{
                          color: '#D3D4E0',
                          border: '1px solid #D3D4E0',
                          borderRadius: '50%',
                          p: 1,
                          '&:hover': {
                            border: `1px solid ${theme.palette.primary.main}`,
                            color: theme.palette.primary.main,
                          },
                        }}
                        icon={social.icon}
                        width={30}
                        height={30}
                      />
                    </IconButton>
                  </a>
                ))}
              </Stack>
            </Stack>
          </Grid>

          <Grid xs={12} md={5}>
            <Stack spacing={5} direction={{ xs: 'column', md: 'row' }}>
              {LINKS.map((list) => (
                <Stack
                  key={list.headline}
                  spacing={2}
                  alignItems={{ xs: 'center', md: 'flex-start' }}
                  sx={{ width: 1 }}
                >
                  <Typography
                    component="div"
                    variant="h6"
                    sx={{
                      color: '#90C8FE',
                    }}
                  >
                    {list.headline}
                  </Typography>

                  {list.children.map((link) => (
                    <Link
                      key={link.name}
                      component={RouterLink}
                      href={link.href}
                      color="common.white"
                      variant="body2"
                    >
                      {link.name}
                    </Link>
                  ))}
                </Stack>
              ))}
            </Stack>
          </Grid>
        </Grid>
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            width: 1,
            bgcolor: theme.palette.primary.main,
            position: 'absolute',
            bottom: 0,
            left: 0,
            height: '70px',
          }}
        >
          <Typography variant="body2" color="common.white">
            {t('footer.copyright')}
          </Typography>
        </Box>
      </Container>
    </Box>
  );

  return mainFooter;
}
