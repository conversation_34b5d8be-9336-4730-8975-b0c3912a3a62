'use client';

// @mui
import Box from '@mui/material/Box';
import { Container } from '@mui/material';
// routes
// import { usePathname } from 'src/routes/hooks';
//
import Footer from './footer';
import Header from './header';

// ----------------------------------------------------------------------

type Props = {
  children: React.ReactNode;
};

export default function MainLayout({ children }: Props) {
  // const pathname = usePathname();

  // const isHome = pathname === '/';

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', height: 1, width: 1 }}>
      <Header />

      <Container
        component="main"
        sx={{
          flexGrow: 1,
          pt: { xs: 8, md: 20 },
          maxWidth: '92% !important',
          width: 1,
        }}
      >
        {children}
      </Container>

      <Footer />
    </Box>
  );
}
