import { Icon } from '@iconify/react';
import { Box, IconButton, Typography } from '@mui/material';
import React from 'react';
import { useTheme } from '@mui/material/styles';

interface QuantitySelectorProps {
  children: React.ReactNode;
  onIncrement: () => void;
  onDecrement: () => void;
}

const QuantitySelector = ({ children, onIncrement, onDecrement }: QuantitySelectorProps) => {
  const theme = useTheme();
  return (
    <Box
      sx={{
        display: 'flex',
        alignItems: 'center',
        height: '42px',
        justifyContent: 'center',
        maxWidth: '141px',
      }}
    >
      <IconButton
        onClick={onDecrement}
        sx={{
          border: 1,
          borderColor: theme.palette.grey[100],
          bgcolor: theme.palette.grey[300],
          borderRadius: '50%',
          mx: '16px',
          color: theme.palette.common.black,
          '&:hover': {
            color: theme.palette.common.white,
            bgcolor: theme.palette.primary.main,
          },
        }}
        size="small"
      >
        <Icon icon="material-symbols:remove-rounded" width="24" height="24" />
      </IconButton>
      <Typography
        sx={{
          px: 2,
          minWidth: '3rem',
          textAlign: 'center',
        }}
      >
        {children}
      </Typography>
      <IconButton
        onClick={onIncrement}
        sx={{
          bgcolor: theme.palette.primary.dark,
          borderRadius: '50%',
          mx: '16px',
          color: theme.palette.common.white,
          '&:hover': {
            bgcolor: theme.palette.primary.main,
          },
        }}
        size="small"
      >
        <Icon icon="material-symbols:add-2" width="24" height="24" />
      </IconButton>
    </Box>
  );
};

export default QuantitySelector;
