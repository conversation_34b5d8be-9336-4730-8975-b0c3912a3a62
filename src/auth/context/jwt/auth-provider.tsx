'use client';

import { useEffect, useReducer, useCallback, useMemo } from 'react';
// utils
import axios, { endpoints } from 'src/utils/axios';
//
import { paths } from 'src/routes/paths';
import { useCartStore } from 'src/store/user/useCartStore';
import { ICart } from 'src/types/cart';
import { AuthContext } from './auth-context';
import { setSession } from './utils';
import { ActionMapType, AuthStateType, AuthUserType, IRegistered, IUser } from '../../types';

// ----------------------------------------------------------------------

// NOTE:
// We only build demo at basic level.
// Customer will need to do some extra handling yourself if you want to extend the logic and other features...

// ----------------------------------------------------------------------

enum Types {
  INITIAL = 'INITIAL',
  LOGIN = 'LOGIN',
  REGISTER = 'REGISTER',
  LOGOUT = 'LOGOUT',
}

type Payload = {
  [Types.INITIAL]: {
    user: AuthUserType;
    token: string | null;
  };
  [Types.LOGIN]: {
    user: AuthUserType;
    token: string | null;
  };
  [Types.REGISTER]: {
    user: { name: string; success: boolean };
    token: string | null;
  };
  [Types.LOGOUT]: undefined;
};

type ActionsType = ActionMapType<Payload>[keyof ActionMapType<Payload>];

// ----------------------------------------------------------------------

const initialState: AuthStateType = {
  token: null,
  user: null,
  loading: true,
};

const reducer = (state: AuthStateType, action: ActionsType): AuthStateType => {
  if (action.type === Types.INITIAL) {
    return {
      loading: false,
      user: action.payload.user,
      token: action.payload.token,
    };
  }
  if (action.type === Types.LOGIN) {
    return {
      ...state,
      loading: false,
      user: action.payload.user,
      token: action.payload.token,
    };
  }
  if (action.type === Types.REGISTER) {
    return {
      ...state,
      loading: false,
      user: null,
      token: action.payload.token,
    };
  }
  if (action.type === Types.LOGOUT) {
    return {
      ...state,
      loading: false,
      user: null,
      token: null,
    };
  }
  return state;
};

// ----------------------------------------------------------------------

const STORAGE_KEY = 'accessToken';

type Props = {
  children: React.ReactNode;
};

export function AuthProvider({ children }: Props) {
  const [state, dispatch] = useReducer(reducer, initialState);

  const initialize = useCallback(async () => {
    try {
      const accessToken = sessionStorage.getItem(STORAGE_KEY);
      const storedUser = sessionStorage.getItem('user');

      if (accessToken && storedUser) {
        setSession(accessToken);
        const user = JSON.parse(storedUser);

        try {
          // Verify token and get fresh user data
          const response = await axios.get(endpoints.profile.me, {
            headers: {
              Authorization: `Bearer ${accessToken}`,
            },
          });
          const { data: freshUserData }: { data: IUser } = response;
          await initializeCart(accessToken);

          // Check if user data has changed
          const hasDataChanged = Object.keys(user).some(
            (key) => user[key as keyof IUser] !== freshUserData[key as keyof IUser]
          );

          if (hasDataChanged) {
            // Update stored user data
            sessionStorage.setItem('user', JSON.stringify(freshUserData));
            dispatch({
              type: Types.INITIAL,
              payload: {
                user: { user: freshUserData, token: accessToken },
                token: accessToken,
              },
            });
          } else {
            // Use stored user data
            dispatch({
              type: Types.INITIAL,
              payload: {
                user: { user, token: accessToken },
                token: accessToken,
              },
            });
          }
        } catch (error) {
          // Token invalid or API error
          console.error('error', error);
          sessionStorage.removeItem(STORAGE_KEY);
          sessionStorage.removeItem('user');
          window.location.href = paths.auth.login;
        }
      } else {
        dispatch({
          type: Types.INITIAL,
          payload: {
            user: null,
            token: null,
          },
        });
      }
    } catch (error) {
      console.error(error);
      dispatch({
        type: Types.INITIAL,
        payload: {
          token: null,
          user: null,
        },
      });
    }
  }, []);

  useEffect(() => {
    initialize();
  }, [initialize]);

  // LOGIN
  const login = useCallback(async (email: string, password: string) => {
    const data = {
      email,
      password,
    };

    const response = await axios.post(endpoints.auth.login, data);

    const { token } = response.data;
    setSession(token);

    if (token) {
      const { data: profile } = await axios.get(endpoints.profile.me, {});
      if (profile) {
        await initializeCart(token);
      }
      sessionStorage.setItem('user', JSON.stringify(profile));

      dispatch({
        type: Types.LOGIN,
        payload: {
          token,
          user: profile,
        },
      });
    }
  }, []);

  // REGISTER
  const register = useCallback(
    async (firstName: string, lastName: string, email: string, password: string) => {
      const data = {
        first_name: firstName,
        last_name: lastName,
        email,
        password,
      };

      const response = await axios.post(endpoints.auth.register, data);

      const { token } = response.data as IRegistered;
      setSession(token);

      if (token) {
        const { data: profile } = await axios.get(endpoints.profile.me, {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        });

        sessionStorage.setItem('user', JSON.stringify(profile));

        dispatch({
          type: Types.REGISTER,
          payload: {
            token,
            user: profile,
          },
        });
      }
    },
    []
  );

  // LOGOUT
  const logout = useCallback(async () => {
    setSession(null);
    dispatch({
      type: Types.LOGOUT,
    });
  }, []);

  // ----------------------------------------------------------------------

  const checkAuthenticated = state.user ? 'authenticated' : 'unauthenticated';

  const status = state.loading ? 'loading' : checkAuthenticated;

  const memoizedValue = useMemo(
    () => ({
      user: state.user,
      method: 'jwt',
      loading: status === 'loading',
      authenticated: status === 'authenticated',
      unauthenticated: status === 'unauthenticated',
      //
      login,
      register,
      logout,
    }),
    [login, logout, register, state.user, status]
  );

  const initializeCart = async (token: string) => {
    const cartResponse = (
      await axios.get(endpoints.profile.carts, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      })
    ).data as ICart;

    if (cartResponse.data?.items?.data) {
      useCartStore.getState().initializeCart(
        cartResponse.data?.items?.data.map((item) => ({
          id: item.id,
          title: item.product.name,
          quantity: item.qty,
          cover: '',
          selected: true,
          price: item.product_price,
        }))
      );
    } else {
      useCartStore.getState().initializeCart([]);
    }
  };

  return <AuthContext.Provider value={memoizedValue}>{children}</AuthContext.Provider>;
}
