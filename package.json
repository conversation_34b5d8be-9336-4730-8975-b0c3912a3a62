{"name": "@minimal-kit/next-ts", "author": "Minimals", "version": "5.4.0", "description": "Next & TypeScript", "private": true, "scripts": {"dev": "next dev -p 8081", "start": "next start", "build": "next build", "export": "next export", "lint": "eslint \"src/**/*.{js,jsx,ts,tsx}\"", "lint:fix": "eslint --fix \"src/**/*.{js,jsx,ts,tsx}\"", "prettier": "prettier --write \"src/**/*.{js,jsx,ts,tsx}\"", "clear-all": "rm -rf node_modules .next out dist build", "re-start": "rm -rf node_modules .next out dist build && yarn install && yarn dev", "re-build": "rm -rf node_modules .next out dist build && yarn install && yarn build", "re-build-test": "rm -rf node_modules .next out dist build && npm install && npm run build"}, "dependencies": {"@auth0/auth0-react": "^2.2.0", "@aws-amplify/auth": "^5.6.0", "@emotion/cache": "^11.10.5", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@fullcalendar/core": "^6.1.8", "@fullcalendar/daygrid": "^6.1.8", "@fullcalendar/interaction": "^6.1.8", "@fullcalendar/list": "^6.1.8", "@fullcalendar/react": "^6.1.8", "@fullcalendar/timegrid": "^6.1.8", "@fullcalendar/timeline": "^6.1.8", "@hello-pangea/dnd": "^16.3.0", "@hookform/resolvers": "^3.2.0", "@iconify/react": "^4.1.1", "@mui/base": "^5.0.0-beta.10", "@mui/lab": "^5.0.0-alpha.139", "@mui/material": "^5.14.4", "@mui/system": "^5.14.4", "@mui/x-data-grid": "^6.11.0", "@mui/x-date-pickers": "^6.11.0", "@react-pdf/renderer": "^3.1.12", "apexcharts": "^3.41.1", "autosuggest-highlight": "^3.3.4", "axios": "^1.4.0", "caniuse-lite": "^1.0.30001700", "date-fns": "^2.30.0", "encoding": "^0.1.13", "firebase": "^10.1.0", "framer-motion": "^10.15.1", "highlight.js": "^11.8.0", "i18next": "^23.4.3", "i18next-browser-languagedetector": "^7.1.0", "lodash": "^4.17.21", "lodash.debounce": "^4.0.8", "mapbox-gl": "^2.15.0", "mui-one-time-password-input": "^2.0.0", "next": "^13.4.13", "notistack": "^3.0.1", "nprogress": "^0.2.0", "numeral": "^2.0.6", "react": "^18.2.0", "react-apexcharts": "^1.4.1", "react-dom": "^18.2.0", "react-dropzone": "^14.2.3", "react-hook-form": "^7.45.4", "react-i18next": "^13.0.3", "react-lazy-load-image-component": "^1.6.0", "react-map-gl": "^7.1.3", "react-markdown": "^8.0.7", "react-organizational-chart": "^2.2.1", "react-quill": "^2.0.0", "react-slick": "^0.29.0", "rehype-highlight": "^6.0.0", "rehype-raw": "^6.1.1", "remark-gfm": "^3.0.1", "simplebar-react": "^3.2.4", "slick-carousel": "^1.8.1", "stylis": "^4.3.0", "stylis-plugin-rtl": "^2.1.1", "swr": "^2.2.0", "yet-another-react-lightbox": "^3.12.0", "yup": "^1.2.0", "zustand": "^5.0.2"}, "devDependencies": {"@babel/core": "^7.22.10", "@babel/plugin-syntax-flow": "^7.22.5", "@babel/plugin-transform-react-jsx": "^7.22.5", "@svgr/webpack": "^8.0.1", "@types/autosuggest-highlight": "^3.2.0", "@types/lodash": "^4.14.196", "@types/mapbox-gl": "^2.7.13", "@types/node": "^20.4.8", "@types/nprogress": "^0.2.0", "@types/numeral": "^2.0.2", "@types/react": "^18.2.19", "@types/react-lazy-load-image-component": "^1.5.3", "@types/react-slick": "^0.23.10", "@types/stylis": "^4.2.0", "@typescript-eslint/eslint-plugin": "^6.3.0", "@typescript-eslint/parser": "^6.3.0", "eslint": "^8.46.0", "eslint-config-airbnb": "^19.0.4", "eslint-config-airbnb-typescript": "^17.1.0", "eslint-config-prettier": "^9.0.0", "eslint-config-react-app": "^7.0.1", "eslint-import-resolver-typescript": "^3.5.5", "eslint-plugin-flowtype": "^8.0.3", "eslint-plugin-import": "^2.28.0", "eslint-plugin-jsx-a11y": "^6.7.1", "eslint-plugin-prettier": "^5.0.0", "eslint-plugin-react": "^7.33.1", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-unused-imports": "^3.0.0", "prettier": "^3.0.1", "typescript": "^5.1.6"}}