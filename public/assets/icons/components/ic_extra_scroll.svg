<svg width="201" height="200" viewBox="0 0 201 200" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_1587_120555)">
<g filter="url(#filter0_di_1587_120555)">
<rect x="24.0137" y="52" width="152" height="96" rx="12" fill="white"/>
</g>
<path d="M176.014 136V64C176.014 57.3726 170.641 52 164.014 52V148C170.641 148 176.014 142.627 176.014 136Z" fill="white"/>
<path opacity="0.16" d="M176.014 136V64C176.014 57.3726 170.641 52 164.014 52V148C170.641 148 176.014 142.627 176.014 136Z" fill="#007867"/>
<g filter="url(#filter1_di_1587_120555)">
<rect x="166.014" y="72" width="8" height="24" rx="4" fill="#FFAB00"/>
</g>
<g filter="url(#filter2_di_1587_120555)">
<rect x="40.0137" y="77" width="28" height="10" rx="5" fill="#00A76F"/>
</g>
<rect opacity="0.4" x="40.0137" y="95" width="64" height="10" rx="5" fill="#007867"/>
<rect opacity="0.16" x="40.0137" y="113" width="80" height="10" rx="5" fill="#007867"/>
</g>
<defs>
<filter id="filter0_di_1587_120555" x="16.0137" y="44" width="184" height="128" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="8" dy="8"/>
<feGaussianBlur stdDeviation="8"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.770709 0 0 0 0 0.792653 0 0 0 0 0.818587 0 0 0 0.16 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1587_120555"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1587_120555" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-2" dy="-2"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.717499 0 0 0 0 0.740813 0 0 0 0 0.768367 0 0 0 0.48 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_1587_120555"/>
</filter>
<filter id="filter1_di_1587_120555" x="162.014" y="68" width="24" height="40" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="4" dy="4"/>
<feGaussianBlur stdDeviation="4"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.717647 0 0 0 0 0.431373 0 0 0 0 0 0 0 0 0.16 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1587_120555"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1587_120555" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-1" dy="-1"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.717647 0 0 0 0 0.431373 0 0 0 0 0 0 0 0 0.48 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_1587_120555"/>
</filter>
<filter id="filter2_di_1587_120555" x="36.0137" y="73" width="44" height="26" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="4" dy="4"/>
<feGaussianBlur stdDeviation="4"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0.470588 0 0 0 0 0.403922 0 0 0 0.16 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1587_120555"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1587_120555" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-1" dy="-1"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0.470588 0 0 0 0 0.403922 0 0 0 0.48 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_1587_120555"/>
</filter>
<clipPath id="clip0_1587_120555">
<rect width="200" height="200" fill="white" transform="translate(0.0136719)"/>
</clipPath>
</defs>
</svg>
