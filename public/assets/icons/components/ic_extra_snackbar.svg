<svg width="201" height="200" viewBox="0 0 201 200" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_1589_121148)">
<g filter="url(#filter0_di_1589_121148)">
<rect x="24.0137" y="64" width="152" height="72" rx="12" fill="white"/>
</g>
<rect opacity="0.4" x="36.0137" y="86" width="28" height="10" rx="5" fill="#007867"/>
<rect opacity="0.16" x="36.0137" y="104" width="64" height="10" rx="5" fill="#007867"/>
<g filter="url(#filter1_di_1589_121148)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M164.014 100C164.014 107.732 157.746 114 150.014 114C142.282 114 136.014 107.732 136.014 100C136.014 92.268 142.282 86 150.014 86C157.746 86 164.014 92.268 164.014 100ZM152.794 100.16L156.63 103.997C157.354 104.72 157.354 105.893 156.631 106.617C156.269 106.979 155.795 107.16 155.32 107.16C154.846 107.16 154.372 106.979 154.01 106.617L150.173 102.78L146.337 106.617C145.975 106.978 145.5 107.159 145.026 107.159C144.552 107.159 144.078 106.979 143.716 106.617C142.992 105.893 142.992 104.72 143.716 103.996L147.553 100.159L143.716 96.3226C142.992 95.5993 142.992 94.4261 143.716 93.7022C144.439 92.9786 145.612 92.9786 146.336 93.7022L150.173 97.5393L154.01 93.7025C154.734 92.9789 155.907 92.9789 156.63 93.7025C157.354 94.4261 157.354 95.5993 156.63 96.3229L152.794 100.16Z" fill="#FFAB00"/>
</g>
</g>
<defs>
<filter id="filter0_di_1589_121148" x="16.0137" y="56" width="184" height="104" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="8" dy="8"/>
<feGaussianBlur stdDeviation="8"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.770709 0 0 0 0 0.792653 0 0 0 0 0.818587 0 0 0 0.16 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1589_121148"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1589_121148" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-2" dy="-2"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.717499 0 0 0 0 0.740813 0 0 0 0 0.768367 0 0 0 0.48 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_1589_121148"/>
</filter>
<filter id="filter1_di_1589_121148" x="132.014" y="82" width="44" height="44" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="4" dy="4"/>
<feGaussianBlur stdDeviation="4"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.717647 0 0 0 0 0.431373 0 0 0 0 0 0 0 0 0.16 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1589_121148"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1589_121148" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-1" dy="-1"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.717647 0 0 0 0 0.431373 0 0 0 0 0 0 0 0 0.48 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_1589_121148"/>
</filter>
<clipPath id="clip0_1589_121148">
<rect width="200" height="200" fill="white" transform="translate(0.0136719)"/>
</clipPath>
</defs>
</svg>
