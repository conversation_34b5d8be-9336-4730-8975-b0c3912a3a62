<svg id="icon_aqeedah" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="75.569" height="75.552" viewBox="0 0 75.569 75.552">
  <defs>
    <clipPath id="clip-path">
      <rect id="Rectangle_161418" data-name="Rectangle 161418" width="75.569" height="75.552" fill="#a66ce0"/>
    </clipPath>
  </defs>
  <g id="Group_151266" data-name="Group 151266" clip-path="url(#clip-path)">
    <path id="Path_84201" data-name="Path 84201" d="M69.618,42.2a54.381,54.381,0,0,1-2.682-6.128c-1.354-4.495-.21-8.59,2.58-12.267a33.732,33.732,0,0,1,9.319-7.988c3.788-2.368,7.687-4.559,11.46-6.948a25.934,25.934,0,0,0,3.622-3.091c.745-.694,1.449-.841,2.045-.093,1.766,2.217,4.222,3.492,6.547,4.957,4.352,2.742,8.8,5.355,12.988,8.323a24.393,24.393,0,0,1,5.341,5.43c3.72,5.041,3.666,11.113.159,16.32-.266.4-.542.784-.846,1.224a17.84,17.84,0,0,1,4.415,8.625,20.8,20.8,0,0,1,.4,3.676c.038,6.991.021,13.982.017,20.973,0,1.57-.315,1.874-1.9,1.883-.6,0-1.2.016-1.8,0-1.292-.039-2.476-.145-3.29,1.349a3.146,3.146,0,0,1-2.4,1.008c-10.469.064-20.938.05-31.406.028a8.727,8.727,0,0,1-5.007-1.743,3.5,3.5,0,0,0-1.823-.612c-3.475-.057-6.952-.025-10.429-.029-1.636,0-1.924-.3-1.925-1.972,0-6.831.014-13.662-.006-20.494A18.148,18.148,0,0,1,69.618,42.2M95.011,8.331a24.052,24.052,0,0,1-2.14,1.7q-4.619,2.885-9.291,5.685c-3.873,2.329-7.725,4.686-10.845,8.023-5.074,5.427-5.262,11.506-.515,17.209.743.894.546,1.51-.189,2.254a15.6,15.6,0,0,0-4.615,11.45c.017,6.275,0,12.55.007,18.824,0,.384.035.767.053,1.159h9.073a1.744,1.744,0,0,0-.058-.405,8.7,8.7,0,0,1,4.892-11.475c1.444-.626,2.822-1.406,4.405-2.2-2.508-2.484-4.9-4.823-7.251-7.21a5.7,5.7,0,0,1-1.266-1.851,4.067,4.067,0,0,1,1.6-4.845,4.209,4.209,0,0,1,5.295.511c1.343,1.291,2.6,2.667,4.019,4.126a15.953,15.953,0,0,1,4.084-7.361,1.5,1.5,0,0,0-.529-2.382,6.971,6.971,0,0,1-3.894-5.927,48.175,48.175,0,0,1,0-5.872A8.35,8.35,0,0,1,95.7,21.949a8.13,8.13,0,0,1,8.684,6.927,42.293,42.293,0,0,1-.005,7.4,5.5,5.5,0,0,1-2.394,4.115c-1.348,1.032-1.355,1.045-.177,2.217a15.831,15.831,0,0,1,5.022,10.2c.232,3.181.126,6.387.17,9.582a8.562,8.562,0,0,1-1.923,5.66,7.006,7.006,0,0,0-.4.65c.986,0,1.824.033,2.659-.006a6.385,6.385,0,0,1,5.787,2.744,3.753,3.753,0,0,0,1.7,1.236c1.348.462,2.844.565,3.737,1.91.049.074.212.1.323.1,1.221.007,2.441,0,3.7,0,0-6.775-.018-13.368.006-19.96a15.661,15.661,0,0,0-4.652-11.556,1.444,1.444,0,0,1-.062-2.339c4.315-4.909,4.417-10.968.311-16.026-2.818-3.471-6.512-5.854-10.248-8.183-4.326-2.7-9.012-4.833-12.925-8.288M104.533,71.1c-5.1,0-10.053-.005-15.009,0-.883,0-1.693-.156-1.684-1.22s.833-1.2,1.708-1.2c2.958.013,5.916.026,8.873,0a5.964,5.964,0,0,0,6.161-6.113c.031-2.717.048-5.435-.009-8.152a13.867,13.867,0,0,0-3.358-8.89c-1.628-1.925-1.636-1.917-4.077-1.145-.342.108-.691.2-1.034.3a8.04,8.04,0,0,0-5.154,6.78c.774-.685,1.412-1.51,2.162-2.217A4.19,4.19,0,0,1,99,55.212c-1.887,1.952-3.748,3.95-5.806,5.71A5.058,5.058,0,0,1,89.79,62.3c-1.8-.259-3.073.535-4.482,1.2a34.787,34.787,0,0,0-3.62,1.851,8.277,8.277,0,0,0-2.326,2,5.744,5.744,0,0,0-.45,6.333,5.9,5.9,0,0,0,5.514,3.39c6.515.036,13.03.013,19.545.009a3.679,3.679,0,0,0,.562-.087ZM90.206,31.577c0,1.119,0,2.112,0,3.106a4.746,4.746,0,0,0,3.667,5.07,1.45,1.45,0,0,1,1.134,1.6c-.018.344.029.69.051,1.113,1.041-.3,2.012-.53,2.937-.881a1.1,1.1,0,0,0,.565-.841,1.724,1.724,0,0,1,1.148-1.768,3.437,3.437,0,0,0,2.2-2.514c.246-1.589.261-3.215.379-4.882ZM82.27,53.759c1.774,1.756,3.52,3.506,5.289,5.231a2.743,2.743,0,0,0,4.227.029c1.8-1.751,3.575-3.535,5.334-5.331a1.782,1.782,0,0,0,.106-2.742,1.827,1.827,0,0,0-2.654.245c-1.089,1.054-2.152,2.137-3.223,3.209C89.772,55.98,89.6,55.992,88,54.361c-1.053-1.074-2.061-2.193-3.057-3.257L82.27,53.759m19.639-24.695c-.113-2.312-2.734-4.762-5.777-4.728-3.137.036-5.609,2.64-5.64,4.728Zm5.14,48.023c2.765,0,5.4.035,8.023-.031.5-.012,1.322-.33,1.413-.665.231-.849-.6-.959-1.219-1.083a6.792,6.792,0,0,1-4.414-2.8,3.557,3.557,0,0,0-3.8-1.326ZM80.53,51.872l2.43-2.439c-.562-.817-1.275-1.389-2.213-1.041a2.394,2.394,0,0,0-1.261,1.256c-.353.939.224,1.652,1.044,2.224" transform="translate(-49.422 -3.944)" fill="#a66ce0"/>
    <path id="Path_84202" data-name="Path 84202" d="M9.646,19.193A9.6,9.6,0,0,1,8.842.026c.779-.059,1.589-.1,1.883.844.276.888-.378,1.267-1.013,1.665A5.034,5.034,0,0,0,7.277,7.729a5.081,5.081,0,0,0,9.4,1.738c.4-.644.784-1.278,1.67-.989.941.308.885,1.122.821,1.9a9.641,9.641,0,0,1-9.523,8.818M5.3,3.959a6.951,6.951,0,0,0-2.666,7.39,7.144,7.144,0,0,0,5.813,5.325A6.747,6.747,0,0,0,15.136,14C8.4,16.1,3.185,10.2,5.3,3.959" transform="translate(0 0.001)" fill="#a66ce0"/>
    <path id="Path_84203" data-name="Path 84203" d="M289.558,6.883c.1-.929.307-1.719,1.355-1.709s1.3.827,1.165,1.682a3.769,3.769,0,0,1-.725,1.708c-.14.174-1,.054-1.233-.2a4.233,4.233,0,0,1-.561-1.478" transform="translate(-220.138 -3.933)" fill="#a66ce0"/>
    <path id="Path_84204" data-name="Path 84204" d="M277.236,20c.888-.044,1.694.134,1.709,1.18s-.772,1.257-1.655,1.223c-.237-.009-.556.091-.7-.027-.435-.362-1.124-.8-1.135-1.22-.029-1.125.91-1.2,1.779-1.157" transform="translate(-209.418 -15.202)" fill="#a66ce0"/>
    <path id="Path_84205" data-name="Path 84205" d="M302.251,22.383c-1.1.053-1.919-.111-1.935-1.158-.017-1.133.853-1.242,1.758-1.243.851,0,1.66.076,1.773,1.119a1.12,1.12,0,0,1-1.114,1.276,2.284,2.284,0,0,1-.482.005" transform="translate(-228.316 -15.191)" fill="#a66ce0"/>
    <path id="Path_84206" data-name="Path 84206" d="M292.531,31.964c.041.9-.144,1.7-1.19,1.708s-1.25-.782-1.215-1.663c.009-.237-.013-.477.013-.712.082-.733.493-1.218,1.237-1.157,1.132.093,1.193.963,1.155,1.823" transform="translate(-220.568 -22.911)" fill="#a66ce0"/>
    <path id="Path_84207" data-name="Path 84207" d="M22.19,256.942c.044.736-.068,1.571-1.024,1.623a1.593,1.593,0,0,1-1.275-.868c-.175-.867-.486-1.958.636-2.526.925-.468,1.668.313,1.663,1.771" transform="translate(-14.988 -193.892)" fill="#a66ce0"/>
    <path id="Path_84208" data-name="Path 84208" d="M6.873,272.6c-.929-.1-1.719-.308-1.711-1.355s.826-1.3,1.681-1.164a3.778,3.778,0,0,1,1.709.723c.174.14.055,1-.2,1.233a4.239,4.239,0,0,1-1.477.564" transform="translate(-3.924 -205.303)" fill="#a66ce0"/>
    <path id="Path_84209" data-name="Path 84209" d="M31.89,272.384c-.9.041-1.7-.136-1.714-1.185s.775-1.253,1.657-1.22c.237.009.556-.091.7.027.435.362,1.125.8,1.134,1.22.024,1.121-.912,1.2-1.774,1.158" transform="translate(-22.941 -205.231)" fill="#a66ce0"/>
    <path id="Path_84210" data-name="Path 84210" d="M22.522,282.232c-.1.93-.312,1.719-1.356,1.709s-1.3-.828-1.163-1.683a3.775,3.775,0,0,1,.724-1.708c.14-.174,1-.053,1.234.2a4.235,4.235,0,0,1,.562,1.478" transform="translate(-15.18 -213.228)" fill="#a66ce0"/>
    <path id="Path_84211" data-name="Path 84211" d="M42.895,166.8c-.464.308-.97.906-1.381.846a1.685,1.685,0,0,1-1.1-1.171c-.047-.315.667-1.029,1.069-1.051.453-.024.937.527,1.407.829v.547" transform="translate(-30.726 -125.765)" fill="#a66ce0"/>
    <path id="Path_84212" data-name="Path 84212" d="M114.471,101.908c.1-.929.307-1.719,1.355-1.709s1.3.827,1.165,1.682a3.769,3.769,0,0,1-.725,1.708c-.14.174-1,.054-1.233-.2a4.234,4.234,0,0,1-.561-1.478" transform="translate(-87.027 -76.177)" fill="#a66ce0"/>
    <path id="Path_84213" data-name="Path 84213" d="M102.045,114.118c1,.182,1.8.372,1.8,1.419s-.8,1.295-1.667,1.191a3.561,3.561,0,0,1-1.6-.51,1.168,1.168,0,0,1-.12-1.147,6.984,6.984,0,0,1,1.584-.953" transform="translate(-76.299 -86.759)" fill="#a66ce0"/>
    <path id="Path_84214" data-name="Path 84214" d="M127.029,117.655c-1-.184-1.8-.371-1.806-1.419s.8-1.294,1.665-1.19a3.57,3.57,0,0,1,1.6.507,1.167,1.167,0,0,1,.12,1.146,6.977,6.977,0,0,1-1.583.956" transform="translate(-95.202 -87.448)" fill="#a66ce0"/>
    <path id="Path_84215" data-name="Path 84215" d="M117.57,127.191c-.1.93-.312,1.719-1.356,1.709s-1.3-.828-1.163-1.683a3.775,3.775,0,0,1,.724-1.708c.14-.174,1-.053,1.234.2a4.236,4.236,0,0,1,.562,1.478" transform="translate(-87.441 -95.357)" fill="#a66ce0"/>
    <path id="Path_84216" data-name="Path 84216" d="M248,81.756c-.46.315-.957.92-1.369.867a1.683,1.683,0,0,1-1.113-1.154c-.052-.315.651-1.039,1.053-1.067.45-.032.942.513,1.415.807l.014.547" transform="translate(-186.654 -61.125)" fill="#a66ce0"/>
    <path id="Path_84217" data-name="Path 84217" d="M271.254,197.795c-.322-.454-.836-.876-.922-1.371a.957.957,0,0,1,1.143-1.145,1.636,1.636,0,0,1,1.112,1.064c.039.45-.5.95-.788,1.429l-.545.023" transform="translate(-205.509 -148.447)" fill="#a66ce0"/>
  </g>
</svg>
