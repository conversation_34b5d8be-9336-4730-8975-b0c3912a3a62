<svg id="icon_seerah" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="72.735" height="72.746" viewBox="0 0 72.735 72.746">
  <defs>
    <clipPath id="clip-path">
      <rect id="Rectangle_161420" data-name="Rectangle 161420" width="72.735" height="72.746" fill="#69c278"/>
    </clipPath>
  </defs>
  <g id="Group_151272" data-name="Group 151272" clip-path="url(#clip-path)">
    <path id="Path_84220" data-name="Path 84220" d="M36.366,0A36.374,36.374,0,1,1,.012,37.386,36.271,36.271,0,0,1,36.366,0M72.312,36.407A35.936,35.936,0,1,0,36.535,72.325,35.87,35.87,0,0,0,72.312,36.407" transform="translate(-0.001 0.001)" fill="#69c278"/>
    <path id="Path_84221" data-name="Path 84221" d="M79.615,44.861A35.161,35.161,0,1,1,45.561,9.628,35.046,35.046,0,0,1,79.615,44.861m-.388-.008A34.756,34.756,0,1,0,45.2,79.551a34.693,34.693,0,0,0,34.023-34.7" transform="translate(-8.131 -8.41)" fill="#69c278"/>
    <path id="Path_84222" data-name="Path 84222" d="M132.29,203.649a9.735,9.735,0,0,0-.117,1.265,5.128,5.128,0,0,1-1.267,3.451,55.345,55.345,0,0,1-4.357,5.065,26.368,26.368,0,0,1-7.371,5.511,8.124,8.124,0,0,1-3.371.842,4.062,4.062,0,0,1-3.794-2.687,10.431,10.431,0,0,1-.429-6.809c.438-1.96,1.211-3.845,1.842-5.761a2.379,2.379,0,0,1,.222-.446c.027-.046.1-.068.323-.215a4.962,4.962,0,0,1-.044.8c-.415,1.72-.912,3.424-1.247,5.16a6.673,6.673,0,0,0,.423,4.157,3.083,3.083,0,0,0,3.394,1.887,8.632,8.632,0,0,0,4.255-1.686,37.1,37.1,0,0,0,9.838-9.5,5.857,5.857,0,0,0,.876-1.505,2.975,2.975,0,0,0-.032-1.563,100.629,100.629,0,0,0-4.93-12.9,5.763,5.763,0,0,1-.729-2.164,17.547,17.547,0,0,1,.587-3.153l.221-.049c.188.254.4.493.56.763a51.257,51.257,0,0,1,5.538,12.208c.556,1.959.9,3.981,1.288,5.984a21.675,21.675,0,0,0,1.7,5.607,9.24,9.24,0,0,0,4.628,4.228c.087-.395.191-.726.231-1.064a6.753,6.753,0,0,1,4.9-5.822c2.018-.727,4.063-1.379,6.1-2.067a1.136,1.136,0,0,0,.544-.342c-.647,0-1.294-.014-1.94,0q-7,.18-14,.375a7.9,7.9,0,0,0-1.3.138c-.4.079-.521-.105-.465-.431a9.3,9.3,0,0,1,2.591-5.65,38.8,38.8,0,0,1,5.7-4.559c1.6-1.011,3.288-1.887,4.936-2.823.124-.07.244-.146.44-.263a11.011,11.011,0,0,0-.75-1c-1.677-1.78-.217-5.6,1.521-6.44a2.277,2.277,0,0,1,2-.077,21.154,21.154,0,0,1,2.949,1.606,20.372,20.372,0,0,1,2.267,1.962,1.4,1.4,0,0,1,.475.714,5.853,5.853,0,0,1-.684,3.615c-.156.293-.7.409-1.083.547q-1.471.526-2.964.994c-.428.135-.6.323-.5.812a6.654,6.654,0,0,1,.018,1.327l-.2.038c-.261-.361-.542-.709-.776-1.086a.52.52,0,0,0-.775-.245c-2.105,1.093-4.281,2.071-6.3,3.311a49.494,49.494,0,0,0-4.441,3.425,1.894,1.894,0,0,0-.213.275c.629.037,1.177.113,1.721.093q10.9-.4,21.807-.823,1.031-.039,2.061-.094c.428-.023.57.152.453.567-.2.722-.427,1.441-.572,2.175A1.345,1.345,0,0,1,163,202.42c-2.2.718-4.4,1.436-6.576,2.2-3.912,1.377-7.822,2.763-11.708,4.208a19.03,19.03,0,0,0-2.692,1.442c-.419.239-.444.536-.051.877a10.688,10.688,0,0,0,3.4,1.952c.939.342,1.889.66,2.843.959a.674.674,0,0,1,.538.73c-.016.813-.019,1.627-.064,2.438-.025.46-.224.808-.78.76a21.808,21.808,0,0,1-9.512-2.794,10.566,10.566,0,0,1-4.843-6.649c-.377-1.407-.634-2.846-.948-4.27-.047-.214-.1-.427-.156-.64l-.153.011m20.658-16.1a7.41,7.41,0,0,0-3.086-1.783,4.149,4.149,0,0,0-1.624.174c-.315.077-.422.368-.181.694.458.618.889,1.256,1.356,1.868.1.126.337.274.448.234.99-.355,1.967-.749,3.088-1.186" transform="translate(-97.299 -159.155)" fill="#69c278"/>
    <path id="Path_84223" data-name="Path 84223" d="M49.108,177.615a11.348,11.348,0,0,0-1.209-.186c-.5-.026-1.053.071-1.306-.5a1.428,1.428,0,0,1,.364-1.558,1,1,0,0,1,1.66-.037,2.669,2.669,0,0,1-.184,4.064c-.271.187-.777.359-.531.85.164.329.421.741.721.828.214.062.628-.3.842-.566a4.012,4.012,0,0,0,.475-1.011,3.433,3.433,0,0,1,1.4-1.788.545.545,0,0,1,.874.206c.86,1.361,1.885,1.384,2.837.086a2.73,2.73,0,0,1,.377-.533c.484,1.127,1.018,1.425,1.761,1.006.174-.1.393-.335.387-.5-.052-1.616-.147-3.23-.232-4.889-2.893.393-5.655.9-7.921-1.595a2.516,2.516,0,0,0,1.126,1.9c.7.42.756.814.3,1.469-.021,0-.052.009-.061,0-1.3-1.45-2.73-.93-4.079-.164-1.138.646-2.184,1.456-3.255,2.215a2.1,2.1,0,0,0-.406.5,1.974,1.974,0,0,0-.56-.077c-.968.17-1.487-.219-1.519-1.214a3.4,3.4,0,0,1,1.665-3.016,8.785,8.785,0,0,0-.849.99,4.08,4.08,0,0,0-.562,1.231c-.2.854.273,1.336,1.124,1.061a10.566,10.566,0,0,0,2.1-1.073c.84-.5,1.616-1.115,2.474-1.582a19.878,19.878,0,0,1,2.218-.889c-.137-.664-.294-1.407-.444-2.15-.172-.851-.168-.778-1.02-.769-1.682.018-3.373.03-5.046-.114-1.49-.129-1.779-.762-.924-1.967a6.9,6.9,0,0,1,1.666-1.34c.6-.436,1.267-.788,1.818-1.275a1.411,1.411,0,0,0,.36-1.012c-.084-1.726-.246-3.449-.353-5.175-.036-.581.007-1.168.014-1.752l.291-.022c.1.577.233,1.15.3,1.731.227,1.9.408,3.813.653,5.714a2.128,2.128,0,0,0,1.746,2.014c-.146-1.688-.286-3.307-.434-5.018l-.838.293c1.211-.466.751-1.438.683-2.252-.088-1.059-.265-2.11-.347-3.169a8.12,8.12,0,0,1,.116-1.365l.306-.008.771,5.663L50.1,159.68l.044.023c-.126.137-.262.267-.377.413-.232.295-.64.469-.536.98.165.806.085,1.694.765,2.319a.555.555,0,0,0,.965-.3c.16-.6.218-1.228.333-1.841a3.529,3.529,0,0,1,.982-2.15c0,.652,0,1.131,0,1.609a11.676,11.676,0,0,0-.011,1.489,1.2,1.2,0,0,0,1.124,1.085,1.169,1.169,0,0,0,1.237-.76,3.69,3.69,0,0,0,.078-2.74,9.055,9.055,0,0,1-.113-1.332l.355-.064c.164.661.342,1.32.487,1.985.1.467.111.957.247,1.412a1.462,1.462,0,0,0,1.111,1.157,1.529,1.529,0,0,0,1.007-.289c.117-.083.053-.537-.038-.781-.356-.955-.783-1.884-1.122-2.844a2.584,2.584,0,0,1,.034-.923l.195-.039.887,1.155-.183.055a2.147,2.147,0,0,0,.055.747,4.332,4.332,0,0,1,.613,2.569c-.122.772-.423,1.163-1.054,1.306a1.363,1.363,0,0,1-1.528-.721c-.175-.273-.305-.575-.473-.9a11.255,11.255,0,0,1-.489,1.164,1.431,1.431,0,0,1-1.507.791,1.294,1.294,0,0,1-1.181-1.129c-.119-.791-.111-1.6-.244-2.445-.1.52-.2,1.04-.311,1.559a11.971,11.971,0,0,1-.359,1.64c-.227.635-.655.742-1.07.214a5.655,5.655,0,0,1-.824-1.7,7.7,7.7,0,0,1-.2-1.456l-.207-.079c-.114.235-.337.476-.325.7.09,1.644.234,3.285.347,4.928.026.384.245.469.575.5a8.9,8.9,0,0,0,5.262-1.276c.575-.324,1.151-.646,1.628-.913l1.09,1.314c.568-.75,1-1.411,1.525-1.983a1.271,1.271,0,0,0,.378-1.167c-.385-2.427-.765-4.855-1.118-7.287a2.856,2.856,0,0,1,.158-.879l.189-.032,1.181,2.631-.734-.293.958,5.824a8.786,8.786,0,0,1,1.927.469,1.613,1.613,0,0,1,.094,2.819,4.5,4.5,0,0,1-.655.363c-.884.419-.886.416-.666,1.543h2.847c-.012.589-.205.867-.753.807a4.6,4.6,0,0,0-.688-.007c-.416.017-.922-.088-1.221.112-.27.18-.311.7-.458,1.069a1.225,1.225,0,0,1-.125.2c-.054-.049-.1-.072-.1-.1-.07-1.1-.069-1.093-1.221-1.071-.411.008-.822,0-1.308,0l.256,2.818c.808-.283,1.51-.514,2.2-.774,1.014-.382,2.009-.818,3.036-1.16a3.562,3.562,0,0,1,1.124,0,2.992,2.992,0,0,1-.224,1.05A21.639,21.639,0,0,1,62.5,172.92c-.247.3-.526.565-.824.883l1.488.556c-.977.226-1.99.386-2.951.7-.986.326-1.072.678-.62,1.6a2.068,2.068,0,0,0,3.332.954c.626-.4,1.217-.853,1.824-1.282l.153.095a3.881,3.881,0,0,1-.568,1.026,7.645,7.645,0,0,1-3.283,1.868,2.341,2.341,0,0,1-3.063-1.487c-.236.815-.331,1.531-1.178,1.695-.741.143-1.006.023-1.564-.919l-.321.465a1.69,1.69,0,0,1-2.95-.019c-.395-.6-.672-.658-1.133-.118a9.951,9.951,0,0,0-.943,1.458,6.243,6.243,0,0,1-.4.843c-.223.27-.551.633-.828.628s-.607-.371-.812-.653a4.7,4.7,0,0,1-.418-1.036,9,9,0,0,1-1.111.6,8.21,8.21,0,0,1-1.894.573,1.714,1.714,0,0,1-1.977-1.759,5,5,0,0,1,.262-1.553,7.714,7.714,0,0,0,.123,1.221,1.357,1.357,0,0,0,1.622,1.133,12.187,12.187,0,0,0,2.668-.982c.266-.13.341-.652.515-1.02l.157.623,1.33-1.15-.039-.251m.318-7.814a3.159,3.159,0,0,0,1.653,1.963,8.541,8.541,0,0,0,5.67.446c.373-.072.467-.271.419-.617-.089-.638-.183-1.277-.227-1.918-.028-.407-.216-.469-.566-.436-.827.078-1.657.123-2.485.19-1.465.119-2.93.245-4.464.373m9.857,8.508.229-.158c-.122-.234-.253-.464-.365-.7a2.352,2.352,0,0,1,1.377-3.343,2.941,2.941,0,0,0,1.634-1.2c.254-.326.569-.6.848-.912.295-.325.579-.659.894-1.018-.967-.4-4.714,1.078-4.97,1.767-.2-.046-.41-.164-.523-.1-.2.108-.508.34-.492.49a38.789,38.789,0,0,0,.549,3.882,4.467,4.467,0,0,0,.82,1.3m-10.973-9.386c.116-.675-.047-1.019-.654-1.22a2.311,2.311,0,0,1-1.541-1.676c-.11-.411-.185-.83-.313-1.409-.138.333-.226.511-.285.7A2.727,2.727,0,0,1,44.035,167c-.468.225-.973.383-1.415.648a5.121,5.121,0,0,0-.817.762c.34.172.675.484,1.02.493,1.82.05,3.642.023,5.488.023m.849-.7c-.081.579,0,.684.6.639,2.119-.16,4.236-.335,6.356-.478a.733.733,0,0,0,.8-.626c.161-.55.223-.94-.331-1.252a.59.59,0,0,1-.231-.289c-.167-.434-.4-.338-.734-.17a22.554,22.554,0,0,1-3.036,1.445,29.355,29.355,0,0,1-3.427.73m11.2-2.059,2.022-1.052a2.313,2.313,0,0,0-2.289-1.133l.267,2.185m-.383,2.01c-.247-.41.157-1-.581-1.038a8.724,8.724,0,0,0-1.609.011.745.745,0,0,0-.513.507c-.01.205.254.6.413.608.715.032,1.435-.043,2.291-.087m-.54-3.663-.207-.1-1.309,1.6.119.151h1.577l-.181-1.65m-11.23,11.855.051-.177a5.825,5.825,0,0,0-.8-.22c-.073-.011-.174.17-.39.4Z" transform="translate(-35.835 -135.401)" fill="#69c278"/>
    <path id="Path_84224" data-name="Path 84224" d="M136.749,85.473c-1.691-.066-3.392-.32-5.048.332a1.381,1.381,0,0,1-.458.013,1.215,1.215,0,0,1,.049-.454A25.808,25.808,0,0,1,132.7,83a1.871,1.871,0,0,1,1.044-.688,13.758,13.758,0,0,1,4.973-.083c.695.109.964.486.654,1.167a24.909,24.909,0,0,1-1.484,2.589c-1.105,1.814-3.066,1.969-4.848,2.46a8.842,8.842,0,0,1-1.044.173c-.034.005-.074-.031-.315-.139.541-.345.968-.664,1.435-.9,1.036-.535,2.094-1.026,3.137-1.548a4.1,4.1,0,0,0,.54-.387l-.04-.169" transform="translate(-114.817 -71.778)" fill="#69c278"/>
    <path id="Path_84225" data-name="Path 84225" d="M194.53,83.016a2.148,2.148,0,0,1,1.181-1.91c1.623-.9,3.215-1.849,4.842-2.738,2.266-1.238,4.521-2.5,6.838-3.64,2.841-1.395,5.742-2.669,8.616-4,.908-.419,1.812-.844,2.721-1.26.237-.108.486-.192.824-.324a1.559,1.559,0,0,1-.993,1.438c-2.856,1.268-5.709,2.555-8.5,3.966-3.843,1.946-7.636,3.994-11.431,6.035-1.371.738-2.685,1.584-4.1,2.429" transform="translate(-170.184 -60.494)" fill="#69c278"/>
    <path id="Path_84226" data-name="Path 84226" d="M342.7,98.463c.175-.178.339-.37.528-.532,2.5-2.133,5.058-4.208,7.491-6.418a33.036,33.036,0,0,0,2.861-3.366c.322-.383.185-.6-.228-.8a8.244,8.244,0,0,1-1.619-.927,1.305,1.305,0,0,1-.432-.994,2.859,2.859,0,0,1,1.29-2.311c.779-.46,1.483-.295,1.761.556a4.92,4.92,0,0,1,.235,2.068.854.854,0,0,0,.586,1.078c.795.368.9.847.462,1.732-.337-.2-.674-.386-1.006-.584-.317-.189-.54-.133-.689.22a14.71,14.71,0,0,1-4.705,5.652c-2.075,1.656-4.216,3.227-6.329,4.836l-.206-.214m11.378-12.137.278-.217a8.2,8.2,0,0,0-.606-1.559,1.157,1.157,0,0,0-1.844.223.388.388,0,0,0,.089.362c.686.412,1.387.8,2.083,1.19" transform="translate(-299.806 -72.497)" fill="#69c278"/>
    <path id="Path_84227" data-name="Path 84227" d="M276.214,120.253a1.243,1.243,0,0,1-.871,1.463q-4.427,2.115-8.79,4.364c-2.33,1.2-4.621,2.468-6.99,3.74a2.215,2.215,0,0,1,1.056-1.666c3.517-1.816,7.015-3.671,10.534-5.485,1.481-.764,3-1.462,4.5-2.188.163-.079.336-.136.566-.229" transform="translate(-227.078 -105.203)" fill="#69c278"/>
    <path id="Path_84228" data-name="Path 84228" d="M449.052,405.82c-.61,2.756-3.694,4.634-5.735,4.845-1.208.125-1.772-.493-1.41-1.66a16.952,16.952,0,0,1,1.129-2.4,3.946,3.946,0,0,1,.357-.5c-.9-.22-1.271-.116-2.321.611a1.9,1.9,0,0,1,.441-1.555,2.909,2.909,0,0,1,2.432-.377.646.646,0,0,1,.237.557,10.177,10.177,0,0,1-.717,1.764c-.266.489-.685.892-.98,1.368-.312.5-.168.837.416.831a6,6,0,0,0,1.951-.321,7.866,7.866,0,0,0,3.761-2.69c.1-.131.223-.24.438-.468" transform="translate(-385.836 -354.002)" fill="#69c278"/>
    <path id="Path_84229" data-name="Path 84229" d="M294.71,209.858l.506,1.71a9.693,9.693,0,0,0,.5,1.595,1.633,1.633,0,0,0,.82.7c.239.094.576-.063.87-.109a2.518,2.518,0,0,0-.136-.735,4.562,4.562,0,0,1-.943-2.781.6.6,0,0,1,.1-.285c.534,1.3,1.632,2.382,1.408,3.951-.076.536-.141,1.087-.72,1.272a1.4,1.4,0,0,1-1.517-.494,9.059,9.059,0,0,1-.625-.889,7.217,7.217,0,0,1-.766,1.3,1.034,1.034,0,0,1-1.8-.246,7.638,7.638,0,0,1-.244-4.324l.249-.016c.018.35.036.7.055,1.051a14.746,14.746,0,0,0,.125,1.845.831.831,0,0,0,.972.692.981.981,0,0,0,.954-.918,4.979,4.979,0,0,0-.2-1.582,2.011,2.011,0,0,1,.179-1.7l.215-.031" transform="translate(-255.432 -183.594)" fill="#69c278"/>
    <path id="Path_84230" data-name="Path 84230" d="M216.531,47.212c-.732.612-1.156-.121-1.669-.334-.486-.2-.346-.649-.221-1.031.3-.913.691-1.048,1.512-.508a6.94,6.94,0,0,1,2.258,2.777c.1.184.194.366.3.545.013.022.073.016.244.047a9.819,9.819,0,0,1,.009-1.29c.15-1.108.307-2.217.536-3.311a.906.906,0,0,1,1.164-.761,1.231,1.231,0,0,1,.908,1.253c0,.2-.034.4-.065.737-.3-.229-.492-.4-.708-.538-.486-.309-1.026-.2-1.145.354a26.4,26.4,0,0,0-.359,2.824,18.758,18.758,0,0,1-.389,2.659c-.819-1.144-1.241-2.505-2.374-3.424" transform="translate(-187.673 -37.896)" fill="#69c278"/>
    <path id="Path_84231" data-name="Path 84231" d="M269.942,474.854c-.305-.244-.5-.419-.718-.572-.59-.422-1.128-.29-1.283.44-.2.95-.229,1.936-.346,2.9-.073.606-.171,1.209-.258,1.813l-.24.04-1.655-3.144-.158.218c-.275-.066-.556-.115-.823-.206a1.991,1.991,0,0,1-.424-.263c-.772-.514-.9-.916-.438-1.716a.758.758,0,0,1,.7-.23,7.781,7.781,0,0,1,1.722,1.472c.507.662.81,1.479,1.313,2.229.028-.423.025-.85.089-1.267.151-.985.29-1.976.521-2.944.114-.478.387-.968,1.049-.843a1.339,1.339,0,0,1,1.023,1.25c0,.237-.039.475-.07.818" transform="translate(-230.375 -413.598)" fill="#69c278"/>
    <path id="Path_84232" data-name="Path 84232" d="M489.652,246.981l-1.447-2.909-.167.188c-.271-.069-.546-.126-.812-.214a1.786,1.786,0,0,1-.309-.209,1.133,1.133,0,0,1-.423-1.815c.257-.4.6-.236.881-.082a4.532,4.532,0,0,1,1.894,2.253,4.469,4.469,0,0,0,.6,1.1c.051-.5.085-1,.156-1.5a17.623,17.623,0,0,1,.342-1.966c.237-.9.749-1.278,1.318-1.067a1.36,1.36,0,0,1,.707,1.941c-.213-.178-.4-.358-.612-.508-.5-.359-.946-.3-1.166.269a7.378,7.378,0,0,0-.351,1.693c-.1.722-.133,1.452-.215,2.176a3.693,3.693,0,0,1-.18.619l-.214.029" transform="translate(-425.357 -210.58)" fill="#69c278"/>
    <path id="Path_84233" data-name="Path 84233" d="M310.6,42.972a2.379,2.379,0,0,1,2.408.886,2.244,2.244,0,0,1-1.089,3.089,1.319,1.319,0,0,1-1.939-1.166,11.4,11.4,0,0,1,.642-3.819l.184.033c-.067.329-.133.657-.2.989-.094.493-.221.979-.281,1.473-.1.8.345,1.352,1.037,1.308a1.7,1.7,0,0,0,1.381-.774,6.444,6.444,0,0,0-.844-.539c-.673-.3-1.418-.5-1.3-1.479" transform="translate(-271.171 -36.71)" fill="#69c278"/>
    <path id="Path_84234" data-name="Path 84234" d="M473.547,127.106c-.14.9-.321,1.79-.4,2.692a3.38,3.38,0,0,0,.158,1.451,1.1,1.1,0,0,0,.789.613c.227.009.479-.413.7-.659a1.354,1.354,0,0,0,.15-.37,2.464,2.464,0,0,1-.646,2.206.8.8,0,0,1-1.186-.268,2.39,2.39,0,0,1-.412-1.219c.127-1.424.363-2.839.565-4.256.011-.074.08-.141.121-.211Z" transform="translate(-413.536 -111.18)" fill="#69c278"/>
    <path id="Path_84235" data-name="Path 84235" d="M161.769,390.09c-.161.661-.36,1.316-.467,1.986a2.406,2.406,0,0,0,.087,1.146.86.86,0,0,0,.642.453c.164.008.371-.318.509-.528a2.144,2.144,0,0,0,.166-.572,2.032,2.032,0,0,1-.622,2.284.89.89,0,0,1-.834-.223,2.172,2.172,0,0,1-.376-1.275c.13-1.121.393-2.228.6-3.34l.29.068" transform="translate(-140.735 -341.209)" fill="#69c278"/>
    <path id="Path_84236" data-name="Path 84236" d="M43.973,282.792H42.436c.26-.341.421-.567.6-.777a.472.472,0,0,1,.227-.092c.709-.2,1.688,0,1.587.221a4.142,4.142,0,0,1-1.033,1.447,2.14,2.14,0,0,1-1.37.256l1.578-.872-.052-.183" transform="translate(-37.125 -246.569)" fill="#69c278"/>
    <path id="Path_84237" data-name="Path 84237" d="M134.164,323.729l1.371-.806-.034-.124h-1.355a1.475,1.475,0,0,1,2.063-.855.545.545,0,0,1,.064.48c-.333.958-.753,1.228-2.109,1.305" transform="translate(-117.357 -281.493)" fill="#69c278"/>
    <path id="Path_84238" data-name="Path 84238" d="M107.445,153.319c2.44-1.371,5.081-2.315,7.607-3.5l.1.212-7.62,3.433-.088-.142" transform="translate(-93.998 -131.066)" fill="#69c278"/>
    <path id="Path_84239" data-name="Path 84239" d="M119.317,306.68l-6.454,2.558.579.66-.161.18c-.168-.151-.495-.331-.473-.447a1.2,1.2,0,0,1,.427-.793c.774-.4,1.595-.705,2.407-1.024,1.005-.394,2.019-.767,3.031-1.144.186-.069.381-.114.573-.17l.072.181" transform="translate(-98.689 -268.14)" fill="#69c278"/>
    <path id="Path_84240" data-name="Path 84240" d="M142.344,360.369c.236.1.368.154.5.2.364.137.558.338.356.756s-.446.179-.721.114c-.179-.043-.523.016-.572.127-.185.414-.409.38-.739.209s-.56-.37-.331-.773c.06-.105.122-.209.2-.343.5.184,1.074.783,1.3-.294" transform="translate(-123.131 -315.268)" fill="#69c278"/>
    <path id="Path_84241" data-name="Path 84241" d="M106.511,145.9l-5.77,2.574.391.708c-.528-.107-.677-.351-.43-.734a1.357,1.357,0,0,1,.563-.463c1.63-.738,3.269-1.456,4.906-2.175a1.267,1.267,0,0,1,.282-.054l.058.143" transform="translate(-87.997 -127.519)" fill="#69c278"/>
    <path id="Path_84242" data-name="Path 84242" d="M55.3,294.253l-5.911,2.641-.075-.162a54.376,54.376,0,0,1,5.9-2.667l.087.189" transform="translate(-43.138 -257.261)" fill="#69c278"/>
    <path id="Path_84243" data-name="Path 84243" d="M120.158,232.186,115.592,234l-.1-.249,4.569-1.8.1.242" transform="translate(-101.037 -202.915)" fill="#69c278"/>
    <path id="Path_84244" data-name="Path 84244" d="M200.622,316.911l-3.906,1.819-.116-.242,3.892-1.814.13.237" transform="translate(-171.995 -277.041)" fill="#69c278"/>
    <path id="Path_84245" data-name="Path 84245" d="M71.653,280.288c-1.356.144-.363,1.321-.866,1.67-.359-.325-.733-.645-1.08-.991-.072-.072-.046-.244-.065-.37.129,0,.312-.05.377.015.241.242.443.523.717.859a8.736,8.736,0,0,1,.142-1.034c.041-.158.188-.382.313-.4s.307.158.463.25" transform="translate(-60.926 -244.988)" fill="#69c278"/>
    <path id="Path_84246" data-name="Path 84246" d="M55.036,226.708c.1.121.334.518.436.488.689-.206.134-.752.333-1.167a3.585,3.585,0,0,0,.224.64,2.566,2.566,0,0,0,.405.442l.172-.109-.4-.941.139-.07a3.336,3.336,0,0,1,.365.825c.053.349-.015.718-.548.464a.369.369,0,0,0-.277-.016c-.722.339-.824.278-.851-.529,0-.06,0-.121,0-.028" transform="translate(-48.147 -197.707)" fill="#69c278"/>
    <path id="Path_84247" data-name="Path 84247" d="M67.559,342.108l-3.216,1.5-.1-.206a30.775,30.775,0,0,1,3.2-1.523l.116.234" transform="translate(-56.201 -299.087)" fill="#69c278"/>
    <path id="Path_84248" data-name="Path 84248" d="M65.069,331.845l.478-1.183.421.2c-1.214.2-.319,1.24-.724,1.63-.358-.288-.719-.557-1.051-.859-.074-.067-.044-.248-.062-.377.129,0,.3-.053.379.009a5.579,5.579,0,0,1,.558.579" transform="translate(-56.106 -289.278)" fill="#69c278"/>
    <path id="Path_84249" data-name="Path 84249" d="M133.719,173.454c.281.256.091.927.774.9l-.25-.707.2-.037.146.939a7.707,7.707,0,0,1-1.054.269.514.514,0,0,1-.426-.216,2.529,2.529,0,0,1-.108-.7l.465.6.257-1.049" transform="translate(-116.352 -151.746)" fill="#69c278"/>
    <path id="Path_84250" data-name="Path 84250" d="M154.4,315.7l.685.758c.048-.325.066-.574.127-.812a1.95,1.95,0,0,1,.212-.408l.442.272-.059.128h-.471l-.128,1.4-.173.015-.889-1.193.255-.162" transform="translate(-134.856 -275.788)" fill="#69c278"/>
    <path id="Path_84251" data-name="Path 84251" d="M135.476,201.96c-.319-.266-.608-.486-.865-.737-.08-.078-.074-.244-.108-.369.141.008.332-.038.414.036a5.287,5.287,0,0,1,.554.726,8.233,8.233,0,0,1,.137-.944.566.566,0,0,1,.273-.348c.069-.028.221.151.437.313-1.2.016-.442.973-.843,1.324" transform="translate(-117.669 -175.25)" fill="#69c278"/>
    <path id="Path_84252" data-name="Path 84252" d="M51.034,209.567l2.66-1.139.113.265-2.672,1.1-.1-.228" transform="translate(-44.647 -182.342)" fill="#69c278"/>
    <path id="Path_84253" data-name="Path 84253" d="M171.955,356.354c-1.091.162-.455,1.1-.785,1.529l-1.058-1.247.248-.2.707.854.57-1.14.319.206" transform="translate(-148.821 -311.575)" fill="#69c278"/>
    <path id="Path_84254" data-name="Path 84254" d="M112.305,181.872a4.11,4.11,0,0,1,.5.289,4.369,4.369,0,0,1,.438.47c-.663,0-1.021-.241-1.09-.725-.026-.183.26-.41.4-.617l.414.4-.085.086-.507-.114-.071.212" transform="translate(-98.114 -158.6)" fill="#69c278"/>
    <path id="Path_84255" data-name="Path 84255" d="M157.351,165.9c-.384-.354-.7-.622-.984-.92-.074-.078-.059-.25-.055-.379,0-.018.221-.077.257-.039.346.372.785.7.782,1.338" transform="translate(-136.745 -143.96)" fill="#69c278"/>
    <path id="Path_84256" data-name="Path 84256" d="M195.2,204.508c-.14-.483-.93-.564-.825-1.186a1.454,1.454,0,0,1,.825,1.186" transform="translate(-170.036 -177.876)" fill="#69c278"/>
    <path id="Path_84257" data-name="Path 84257" d="M60.261,192.926l-.73-1.3.23-.114.651,1.342-.15.07" transform="translate(-52.08 -167.545)" fill="#69c278"/>
    <path id="Path_84258" data-name="Path 84258" d="M130.01,271.879l-4.36,1.819-.1-.227,4.35-1.841.109.249" transform="translate(-109.839 -237.635)" fill="#69c278"/>
    <path id="Path_84259" data-name="Path 84259" d="M148.477,284.873c-.311-.292-.6-.544-.859-.816a.492.492,0,0,1-.092-.31c0-.036.123-.108.189-.107.264.005.835.943.762,1.233" transform="translate(-129.063 -248.141)" fill="#69c278"/>
    <path id="Path_84260" data-name="Path 84260" d="M194.272,294.493l.791.765.138-.11-.15.733-1.035-1.206.256-.183" transform="translate(-169.734 -257.636)" fill="#69c278"/>
    <path id="Path_84261" data-name="Path 84261" d="M73.957,255.274l.2.777.48-.2a1.9,1.9,0,0,1-.3.486.412.412,0,0,1-.356-.087c-.072-.338-.094-.686-.133-1.03l.114.05" transform="translate(-64.601 -223.282)" fill="#69c278"/>
  </g>
</svg>
